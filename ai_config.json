{"agent": {"name": "general-ai-agent", "version": "1.0.0", "type": "log_analyzer", "description": "General purpose AI agent for data analysis"}, "data_sources": {"primary": {"type": "json_file", "path": "logs.json", "format": "log_index", "detailed_logs_path": "logs/"}}, "ai_provider": {"type": "openai_compatible", "api_url": "http://localhost:1234/v1/chat/completions", "model_name": "qwen/qwen3-4b-thinking-2507", "temperature": 0.1, "max_tokens": 2000, "timeout_seconds": 60}, "processing": {"check_interval": 30, "max_retries": 3, "batch_size": 10, "parallel_processing": false, "failure_detection": {"enabled": true, "keywords": ["error", "failed", "failure", "exception", "timeout", "rejected", "declined", "invalid"], "log_types": ["error", "critical"]}}, "analysis": {"domain": "general", "prompt_template": "default", "severity_threshold": "medium", "categories": ["infrastructure", "application", "security", "performance"], "output_format": "json"}, "notifications": {"enabled": true, "channels": {"webhook": {"enabled": false, "url": ""}, "slack": {"enabled": false, "webhook_url": ""}, "console": {"enabled": true}}}, "output": {"reports_directory": "reports/", "filename_pattern": "analysis_{id}_{timestamp}.json", "include_raw_data": false, "pretty_print": true}}