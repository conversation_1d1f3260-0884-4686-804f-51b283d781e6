{"id": "889f5ec8-5657-4a5c-9b0e-ed0575fb851d", "title": "Order Payment Request", "entity": "order", "entity_id": "26a059dc-6f58-4296-9824-dee9ffe554f5", "type": "info", "ip": "**************", "proto": "grpc", "user": "<PERSON>", "user_id": "64982734-fc7c-4c5b-be8c-24643b6d52ef", "admin": "-", "admin_id": "", "message": "{\"VposName\":\"PaycellWFMEcuzdan\",\"VposID\":\"617e0f3f-c6dc-4faa-9f4f-d51061ae2a48\",\"OrderReferenceID\":\"26a059dc-6f58-4296-9824-dee9ffe554f5\",\"CardID\":\"d7d217eb-629a-4b53-bc44-c3271cf36a4d\",\"MaskedCard\":\"\",\"Installment\":\"1\",\"Secure3D\":true,\"Amount\":\"1096.93\",\"OrderPaymentID\":\"c0b72dd4-bdcb-4d41-a40a-7678b27f1c58\",\"CardHolder\":\"\"}", "organization": "WFM", "created_at": "2025-08-22 22:01:40", "request_id": "", "request": {"path": "", "method": "", "headers": null, "body": ""}, "level": 0, "location": "/src/mono-acquiring-admin/app/api/grpcroutes/order/v1/proccess_order_on_gateway.go:1572"}