{"id": "454929a2-ca66-435d-b8e1-87ca86d8dd08", "title": "Paycell topup pre auth", "entity": "order", "entity_id": "26a059dc-6f58-4296-9824-dee9ffe554f5", "type": "info", "ip": "**************", "proto": "grpc", "user": "<PERSON>", "user_id": "********-fc7c-4c5b-be8c-24643b6d52ef", "admin": "-", "admin_id": "", "message": "{\"hashData\":\"\",\"accountId\":\"**********\",\"relAccountId\":\"05f29f0d-d849-488f-970c-0cf5231941b1\",\"orderId\":\"26a059dc-6f58-4296-9824-dee9ffe554f5\",\"topupAmount\":\"107850\",\"currency\":\"TRY\",\"fsmDefTemplateId\":\"88\",\"accountType\":\"MSISDN\",\"relAccountType\":\"CREDITCARD\",\"trnType\":\"WALLET_TOP_UP\",\"channel\":\"TOGG\",\"identityNoType\":\"IDENTITY\",\"countryCode\":\"TR\",\"cardToken\":\"\",\"identityNo\":\"***********\",\"cardBin\":\"545044\",\"installmentCount\":\"1\"}", "organization": "WFM", "created_at": "2025-08-22 22:01:39", "request_id": "", "request": {"path": "", "method": "", "headers": null, "body": ""}, "level": 0, "location": "/src/mono-acquiring-admin/app/api/grpcroutes/transaction/v1/paycell/deposit.go:463"}