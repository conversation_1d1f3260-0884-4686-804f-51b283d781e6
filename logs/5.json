{"id": "3f8561d0-64dd-44b5-9735-586712c7feba", "title": "Paycell topup post auth", "entity": "order", "entity_id": "26a059dc-6f58-4296-9824-dee9ffe554f5", "type": "info", "ip": "**************", "proto": "grpc", "user": "<PERSON>", "user_id": "********-fc7c-4c5b-be8c-24643b6d52ef", "admin": "-", "admin_id": "", "message": "{\"hashData\":\"\",\"accountId\":\"**********\",\"relAccountId\":\"05f29f0d-d849-488f-970c-0cf5231941b1\",\"cardToken\":\"\",\"orderId\":\"26a059dc-6f58-4296-9824-dee9ffe554f5\",\"topupAmount\":\"107850\",\"identityNo\":\"***********\",\"fsmDefTemplateId\":\"88\",\"accountType\":\"MSISDN\",\"relAccountType\":\"CREDITCARD\",\"trnType\":\"WALLET_TOP_UP\",\"channel\":\"TOGG\",\"cardBin\":\"545044\",\"cardLastFourDigit\":\"0449\",\"installmentCount\":\"1\",\"identityNoType\":\"IDENTITY\",\"countryCode\":\"TR\",\"threeDSessionID\":\"f3e20c35-1bac-478f-8e53-ca43a9d23fbb\"}", "organization": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-08-22 22:02:33", "request_id": "", "request": {"path": "", "method": "", "headers": null, "body": ""}, "level": 0, "location": "/src/mono-acquiring-admin/app/api/grpcroutes/callback/v1/paycell_topup_callback.go:168"}