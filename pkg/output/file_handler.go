package output

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"general-ai-agent-structure/pkg/agent"
)

// FileOutputHandler implements OutputHandler for file-based output
type FileOutputHandler struct {
	reportsDirectory string
	filenamePattern  string
	includeRawData   bool
	prettyPrint      bool
}

// NewFileOutputHandler creates a new file output handler
func NewFileOutputHandler() *FileOutputHandler {
	return &FileOutputHandler{}
}

// Initialize initializes the file output handler
func (h *FileOutputHandler) Initialize(config map[string]interface{}) error {
	// Extract configuration
	if reportsDir, ok := config["reports_directory"].(string); ok {
		h.reportsDirectory = reportsDir
	} else {
		h.reportsDirectory = "reports/"
	}
	
	if pattern, ok := config["filename_pattern"].(string); ok {
		h.filenamePattern = pattern
	} else {
		h.filenamePattern = "analysis_{id}_{timestamp}.json"
	}
	
	if includeRaw, ok := config["include_raw_data"].(bool); ok {
		h.includeRawData = includeRaw
	}
	
	if prettyPrint, ok := config["pretty_print"].(bool); ok {
		h.prettyPrint = prettyPrint
	} else {
		h.prettyPrint = true
	}
	
	// Create reports directory if it doesn't exist
	if err := os.MkdirAll(h.reportsDirectory, 0755); err != nil {
		return fmt.Errorf("failed to create reports directory: %w", err)
	}
	
	return nil
}

// SaveReport saves the analysis report to a file
func (h *FileOutputHandler) SaveReport(ctx context.Context, report agent.Report) error {
	// Generate filename
	filename := h.generateFilename(report)
	fullPath := filepath.Join(h.reportsDirectory, filename)
	
	// Prepare report data
	reportData := h.prepareReportData(report)
	
	// Marshal to JSON
	var data []byte
	var err error
	
	if h.prettyPrint {
		data, err = json.MarshalIndent(reportData, "", "  ")
	} else {
		data, err = json.Marshal(reportData)
	}
	
	if err != nil {
		return fmt.Errorf("failed to marshal report: %w", err)
	}
	
	// Write to file
	if err := os.WriteFile(fullPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write report file: %w", err)
	}
	
	return nil
}

// generateFilename generates a filename based on the pattern
func (h *FileOutputHandler) generateFilename(report agent.Report) string {
	filename := h.filenamePattern
	
	// Replace placeholders
	filename = strings.ReplaceAll(filename, "{id}", report.DataEntry.ID)
	filename = strings.ReplaceAll(filename, "{timestamp}", fmt.Sprintf("%d", time.Now().Unix()))
	filename = strings.ReplaceAll(filename, "{date}", time.Now().Format("2006-01-02"))
	filename = strings.ReplaceAll(filename, "{time}", time.Now().Format("15-04-05"))
	filename = strings.ReplaceAll(filename, "{severity}", report.Analysis.Severity)
	filename = strings.ReplaceAll(filename, "{category}", report.Analysis.Category)
	
	// Ensure .json extension
	if !strings.HasSuffix(filename, ".json") {
		filename += ".json"
	}
	
	return filename
}

// prepareReportData prepares the report data for output
func (h *FileOutputHandler) prepareReportData(report agent.Report) map[string]interface{} {
	data := map[string]interface{}{
		"timestamp":    report.Timestamp,
		"agent_info":   report.AgentInfo,
		"analysis":     report.Analysis,
		"processed_by": report.ProcessedBy,
	}
	
	// Add data entry (optionally without raw data)
	if h.includeRawData {
		data["data_entry"] = report.DataEntry
	} else {
		// Include only essential fields
		data["data_entry"] = map[string]interface{}{
			"id":       report.DataEntry.ID,
			"type":     report.DataEntry.Type,
			"title":    report.DataEntry.Title,
			"message":  report.DataEntry.Message,
			"entity":   report.DataEntry.Entity,
			"location": report.DataEntry.Location,
		}
	}
	
	// Add metadata if present
	if report.Metadata != nil {
		data["metadata"] = report.Metadata
	}
	
	return data
}

// Close closes the file output handler
func (h *FileOutputHandler) Close() error {
	// No resources to close for file-based output
	return nil
}

// DatabaseOutputHandler implements OutputHandler for database-based output
type DatabaseOutputHandler struct {
	connectionString string
	tableName        string
	// Add database connection fields as needed
}

// NewDatabaseOutputHandler creates a new database output handler
func NewDatabaseOutputHandler() *DatabaseOutputHandler {
	return &DatabaseOutputHandler{}
}

// Initialize initializes the database output handler
func (h *DatabaseOutputHandler) Initialize(config map[string]interface{}) error {
	connStr, ok := config["connection_string"].(string)
	if !ok || connStr == "" {
		return fmt.Errorf("connection_string is required for database output handler")
	}
	h.connectionString = connStr
	
	if tableName, ok := config["table_name"].(string); ok {
		h.tableName = tableName
	} else {
		h.tableName = "analysis_reports"
	}
	
	// TODO: Initialize database connection
	return fmt.Errorf("database output handler not implemented yet")
}

// SaveReport saves the analysis report to database
func (h *DatabaseOutputHandler) SaveReport(ctx context.Context, report agent.Report) error {
	// TODO: Implement database save
	return fmt.Errorf("database output handler not implemented yet")
}

// Close closes the database connection
func (h *DatabaseOutputHandler) Close() error {
	// TODO: Close database connection
	return nil
}

// APIOutputHandler implements OutputHandler for API-based output
type APIOutputHandler struct {
	apiURL string
	apiKey string
	// Add HTTP client fields as needed
}

// NewAPIOutputHandler creates a new API output handler
func NewAPIOutputHandler() *APIOutputHandler {
	return &APIOutputHandler{}
}

// Initialize initializes the API output handler
func (h *APIOutputHandler) Initialize(config map[string]interface{}) error {
	apiURL, ok := config["api_url"].(string)
	if !ok || apiURL == "" {
		return fmt.Errorf("api_url is required for API output handler")
	}
	h.apiURL = apiURL
	
	if apiKey, ok := config["api_key"].(string); ok {
		h.apiKey = apiKey
	}
	
	// TODO: Initialize HTTP client
	return fmt.Errorf("API output handler not implemented yet")
}

// SaveReport saves the analysis report via API
func (h *APIOutputHandler) SaveReport(ctx context.Context, report agent.Report) error {
	// TODO: Implement API call
	return fmt.Errorf("API output handler not implemented yet")
}

// Close closes the API output handler
func (h *APIOutputHandler) Close() error {
	// TODO: Close HTTP client
	return nil
}

// ConsoleOutputHandler implements OutputHandler for console output
type ConsoleOutputHandler struct {
	prettyPrint bool
	colorOutput bool
}

// NewConsoleOutputHandler creates a new console output handler
func NewConsoleOutputHandler() *ConsoleOutputHandler {
	return &ConsoleOutputHandler{}
}

// Initialize initializes the console output handler
func (h *ConsoleOutputHandler) Initialize(config map[string]interface{}) error {
	if prettyPrint, ok := config["pretty_print"].(bool); ok {
		h.prettyPrint = prettyPrint
	} else {
		h.prettyPrint = true
	}
	
	if colorOutput, ok := config["color_output"].(bool); ok {
		h.colorOutput = colorOutput
	} else {
		h.colorOutput = true
	}
	
	return nil
}

// SaveReport outputs the analysis report to console
func (h *ConsoleOutputHandler) SaveReport(ctx context.Context, report agent.Report) error {
	if h.prettyPrint {
		h.printPrettyReport(report)
	} else {
		data, err := json.Marshal(report)
		if err != nil {
			return fmt.Errorf("failed to marshal report: %w", err)
		}
		fmt.Println(string(data))
	}
	
	return nil
}

// printPrettyReport prints a formatted report to console
func (h *ConsoleOutputHandler) printPrettyReport(report agent.Report) {
	fmt.Printf("\n=== Analysis Report ===\n")
	fmt.Printf("ID: %s\n", report.DataEntry.ID)
	fmt.Printf("Title: %s\n", report.DataEntry.Title)
	fmt.Printf("Timestamp: %s\n", report.Timestamp)
	fmt.Printf("\n--- Analysis ---\n")
	fmt.Printf("Error Type: %s\n", report.Analysis.ErrorType)
	fmt.Printf("Severity: %s\n", report.Analysis.Severity)
	fmt.Printf("Category: %s\n", report.Analysis.Category)
	fmt.Printf("Description: %s\n", report.Analysis.Description)
	fmt.Printf("Root Cause: %s\n", report.Analysis.RootCause)
	fmt.Printf("Business Impact: %s\n", report.Analysis.BusinessImpact)
	fmt.Printf("Suggested Action: %s\n", report.Analysis.SuggestedAction)
	fmt.Printf("Is Retryable: %t\n", report.Analysis.IsRetryable)
	fmt.Printf("Compliance Risk: %s\n", report.Analysis.ComplianceRisk)
	fmt.Printf("Customer Impact: %s\n", report.Analysis.CustomerImpact)
	fmt.Printf("======================\n\n")
}

// Close closes the console output handler
func (h *ConsoleOutputHandler) Close() error {
	// No resources to close for console output
	return nil
}
