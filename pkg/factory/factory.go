package factory

import (
	"fmt"

	"general-ai-agent-structure/pkg/agent"
	"general-ai-agent-structure/pkg/aiprovider"
	"general-ai-agent-structure/pkg/datasource"
	"general-ai-agent-structure/pkg/detector"
	"general-ai-agent-structure/pkg/notification"
	"general-ai-agent-structure/pkg/output"
	"general-ai-agent-structure/pkg/prompt"
)

// ComponentFactory creates various agent components based on configuration
type ComponentFactory struct{}

// NewComponentFactory creates a new component factory
func NewComponentFactory() *ComponentFactory {
	return &ComponentFactory{}
}

// CreateDataSource creates a data source based on type
func (f *ComponentFactory) CreateDataSource(dsType string) (agent.DataSource, error) {
	switch dsType {
	case "json_file":
		return datasource.NewJSONFileDataSource(), nil
	case "database":
		return datasource.NewDatabaseDataSource(), nil
	case "api":
		return datasource.NewAPIDataSource(), nil
	default:
		return nil, fmt.Errorf("unsupported data source type: %s", dsType)
	}
}

// CreateAIProvider creates an AI provider based on type
func (f *ComponentFactory) CreateAIProvider(providerType string) (agent.AIProvider, error) {
	switch providerType {
	case "openai_compatible":
		return aiprovider.NewOpenAICompatibleProvider(), nil
	case "anthropic":
		return aiprovider.NewAnthropicProvider(), nil
	default:
		return nil, fmt.Errorf("unsupported AI provider type: %s", providerType)
	}
}

// CreateOutputHandler creates an output handler based on type
func (f *ComponentFactory) CreateOutputHandler(handlerType string) (agent.OutputHandler, error) {
	switch handlerType {
	case "file":
		return output.NewFileOutputHandler(), nil
	case "database":
		return output.NewDatabaseOutputHandler(), nil
	case "api":
		return output.NewAPIOutputHandler(), nil
	case "console":
		return output.NewConsoleOutputHandler(), nil
	default:
		return nil, fmt.Errorf("unsupported output handler type: %s", handlerType)
	}
}

// CreateNotificationHandler creates a notification handler based on type
func (f *ComponentFactory) CreateNotificationHandler(handlerType string) (agent.NotificationHandler, error) {
	switch handlerType {
	case "console":
		return notification.NewConsoleNotificationHandler(), nil
	case "webhook":
		return notification.NewWebhookNotificationHandler(), nil
	case "slack":
		return notification.NewSlackNotificationHandler(), nil
	case "email":
		return notification.NewEmailNotificationHandler(), nil
	case "composite":
		return notification.NewCompositeNotificationHandler(), nil
	default:
		return nil, fmt.Errorf("unsupported notification handler type: %s", handlerType)
	}
}

// CreatePromptBuilder creates a prompt builder based on domain
func (f *ComponentFactory) CreatePromptBuilder(domain string) agent.PromptBuilder {
	return prompt.NewTemplateBuilder(domain)
}

// CreateFailureDetector creates a failure detector based on type
func (f *ComponentFactory) CreateFailureDetector(detectorType string) (agent.FailureDetector, error) {
	switch detectorType {
	case "keyword":
		return detector.NewKeywordFailureDetector(), nil
	case "regex":
		return detector.NewRegexFailureDetector(), nil
	case "ml":
		return detector.NewMLFailureDetector(), nil
	case "composite":
		return detector.NewCompositeFailureDetector("any"), nil
	default:
		return nil, fmt.Errorf("unsupported failure detector type: %s", detectorType)
	}
}

// CreateCompositeNotificationHandler creates a composite notification handler with multiple handlers
func (f *ComponentFactory) CreateCompositeNotificationHandler(handlerTypes []string) (agent.NotificationHandler, error) {
	composite := notification.NewCompositeNotificationHandler()
	
	for _, handlerType := range handlerTypes {
		handler, err := f.CreateNotificationHandler(handlerType)
		if err != nil {
			return nil, fmt.Errorf("failed to create notification handler %s: %w", handlerType, err)
		}
		composite.AddHandler(handler)
	}
	
	return composite, nil
}

// CreateCompositeFailureDetector creates a composite failure detector with multiple detectors
func (f *ComponentFactory) CreateCompositeFailureDetector(detectorTypes []string, strategy string) (agent.FailureDetector, error) {
	composite := detector.NewCompositeFailureDetector(strategy)
	
	for _, detectorType := range detectorTypes {
		detector, err := f.CreateFailureDetector(detectorType)
		if err != nil {
			return nil, fmt.Errorf("failed to create failure detector %s: %w", detectorType, err)
		}
		composite.AddDetector(detector)
	}
	
	return composite, nil
}

// AgentBuilder helps build a complete agent with all components
type AgentBuilder struct {
	factory *ComponentFactory
	agent   *agent.BaseAgent
}

// NewAgentBuilder creates a new agent builder
func NewAgentBuilder() *AgentBuilder {
	return &AgentBuilder{
		factory: NewComponentFactory(),
		agent:   agent.NewBaseAgent(),
	}
}

// WithConfig initializes the agent with configuration
func (b *AgentBuilder) WithConfig(configPath string) *AgentBuilder {
	if err := b.agent.Initialize(configPath); err != nil {
		panic(fmt.Sprintf("failed to initialize agent: %v", err))
	}
	return b
}

// WithDataSource sets up the data source
func (b *AgentBuilder) WithDataSource(dsType string) *AgentBuilder {
	ds, err := b.factory.CreateDataSource(dsType)
	if err != nil {
		panic(fmt.Sprintf("failed to create data source: %v", err))
	}
	
	if err := b.agent.SetDataSource(ds); err != nil {
		panic(fmt.Sprintf("failed to set data source: %v", err))
	}
	
	return b
}

// WithAIProvider sets up the AI provider
func (b *AgentBuilder) WithAIProvider(providerType string) *AgentBuilder {
	provider, err := b.factory.CreateAIProvider(providerType)
	if err != nil {
		panic(fmt.Sprintf("failed to create AI provider: %v", err))
	}
	
	if err := b.agent.SetAIProvider(provider); err != nil {
		panic(fmt.Sprintf("failed to set AI provider: %v", err))
	}
	
	return b
}

// WithOutputHandler sets up the output handler
func (b *AgentBuilder) WithOutputHandler(handlerType string) *AgentBuilder {
	handler, err := b.factory.CreateOutputHandler(handlerType)
	if err != nil {
		panic(fmt.Sprintf("failed to create output handler: %v", err))
	}
	
	if err := b.agent.SetOutputHandler(handler); err != nil {
		panic(fmt.Sprintf("failed to set output handler: %v", err))
	}
	
	return b
}

// WithNotificationHandler sets up the notification handler
func (b *AgentBuilder) WithNotificationHandler(handlerType string) *AgentBuilder {
	handler, err := b.factory.CreateNotificationHandler(handlerType)
	if err != nil {
		panic(fmt.Sprintf("failed to create notification handler: %v", err))
	}
	
	if err := b.agent.SetNotificationHandler(handler); err != nil {
		panic(fmt.Sprintf("failed to set notification handler: %v", err))
	}
	
	return b
}

// WithPromptBuilder sets up the prompt builder
func (b *AgentBuilder) WithPromptBuilder(domain string) *AgentBuilder {
	builder := b.factory.CreatePromptBuilder(domain)
	b.agent.SetPromptBuilder(builder)
	return b
}

// WithFailureDetector sets up the failure detector
func (b *AgentBuilder) WithFailureDetector(detectorType string) *AgentBuilder {
	detector, err := b.factory.CreateFailureDetector(detectorType)
	if err != nil {
		panic(fmt.Sprintf("failed to create failure detector: %v", err))
	}
	
	if err := b.agent.SetFailureDetector(detector); err != nil {
		panic(fmt.Sprintf("failed to set failure detector: %v", err))
	}
	
	return b
}

// WithPlugin adds a plugin to the agent
func (b *AgentBuilder) WithPlugin(plugin agent.Plugin) *AgentBuilder {
	if err := b.agent.AddPlugin(plugin); err != nil {
		panic(fmt.Sprintf("failed to add plugin: %v", err))
	}
	return b
}

// Build returns the configured agent
func (b *AgentBuilder) Build() agent.Agent {
	return b.agent
}

// BuildDefault creates an agent with default components based on configuration
func (b *AgentBuilder) BuildDefault(configPath string) agent.Agent {
	return b.WithConfig(configPath).
		WithDataSource("json_file").
		WithAIProvider("openai_compatible").
		WithOutputHandler("file").
		WithNotificationHandler("console").
		WithPromptBuilder("general").
		WithFailureDetector("keyword").
		Build()
}
