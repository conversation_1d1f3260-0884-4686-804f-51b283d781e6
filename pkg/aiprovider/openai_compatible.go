package aiprovider

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"general-ai-agent-structure/pkg/agent"
)

// OpenAICompatibleProvider implements AIProvider for OpenAI-compatible APIs
type OpenAICompatibleProvider struct {
	client      *resty.Client
	apiURL      string
	modelName   string
	temperature float64
	maxTokens   int
	apiKey      string
}

// OpenAIRequest represents the request structure for OpenAI-compatible APIs
type OpenAIRequest struct {
	Model       string              `json:"model"`
	Messages    []map[string]string `json:"messages"`
	MaxTokens   int                 `json:"max_tokens"`
	Temperature float64             `json:"temperature"`
}

// OpenAIResponse represents the response structure from OpenAI-compatible APIs
type OpenAIResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Error *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
	} `json:"error,omitempty"`
}

// NewOpenAICompatibleProvider creates a new OpenAI-compatible provider
func NewOpenAICompatibleProvider() *OpenAICompatibleProvider {
	return &OpenAICompatibleProvider{}
}

// Initialize initializes the OpenAI-compatible provider
func (p *OpenAICompatibleProvider) Initialize(config map[string]interface{}) error {
	// Extract configuration
	apiURL, ok := config["api_url"].(string)
	if !ok || apiURL == "" {
		return fmt.Errorf("api_url is required")
	}
	p.apiURL = apiURL
	
	modelName, ok := config["model_name"].(string)
	if !ok || modelName == "" {
		return fmt.Errorf("model_name is required")
	}
	p.modelName = modelName
	
	if temp, ok := config["temperature"].(float64); ok {
		p.temperature = temp
	} else {
		p.temperature = 0.1 // default
	}
	
	if maxTokens, ok := config["max_tokens"].(int); ok {
		p.maxTokens = maxTokens
	} else {
		p.maxTokens = 2000 // default
	}
	
	if apiKey, ok := config["api_key"].(string); ok {
		p.apiKey = apiKey
	}
	
	// Initialize HTTP client
	p.client = resty.New()
	if timeoutSeconds, ok := config["timeout_seconds"].(int); ok {
		p.client.SetTimeout(time.Duration(timeoutSeconds) * time.Second)
	} else {
		p.client.SetTimeout(60 * time.Second)
	}
	
	return nil
}

// Analyze performs AI analysis on the given data
func (p *OpenAICompatibleProvider) Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*agent.Analysis, error) {
	request := OpenAIRequest{
		Model: p.modelName,
		Messages: []map[string]string{
			{
				"role":    "user",
				"content": prompt,
			},
		},
		MaxTokens:   p.maxTokens,
		Temperature: p.temperature,
	}
	
	// Prepare request
	req := p.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(request)
	
	// Add API key if provided
	if p.apiKey != "" {
		req.SetHeader("Authorization", "Bearer "+p.apiKey)
	}
	
	// Make request
	resp, err := req.Post(p.apiURL)
	if err != nil {
		return nil, fmt.Errorf("API request failed: %w", err)
	}
	
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}
	
	// Parse response
	var apiResponse OpenAIResponse
	if err := json.Unmarshal(resp.Body(), &apiResponse); err != nil {
		return nil, fmt.Errorf("failed to parse API response: %w", err)
	}
	
	if apiResponse.Error != nil {
		return nil, fmt.Errorf("API error: %s", apiResponse.Error.Message)
	}
	
	if len(apiResponse.Choices) == 0 {
		return nil, fmt.Errorf("no choices in API response")
	}
	
	aiContent := apiResponse.Choices[0].Message.Content
	
	// Extract JSON from the AI response
	analysis, err := p.extractJSONFromResponse(aiContent)
	if err != nil {
		return p.createFallbackAnalysis(fmt.Sprintf("Failed to parse AI response: %v", err)), nil
	}
	
	return analysis, nil
}

// extractJSONFromResponse extracts JSON from AI response that may contain thinking tags
func (p *OpenAICompatibleProvider) extractJSONFromResponse(text string) (*agent.Analysis, error) {
	// Remove think tags
	thinkPattern := regexp.MustCompile(`(?s)<think>.*?</think>`)
	cleanedText := thinkPattern.ReplaceAllString(text, "")
	
	// Clean up the text - remove extra whitespace and normalize
	cleanedText = regexp.MustCompile(`\s+`).ReplaceAllString(cleanedText, " ")
	cleanedText = strings.TrimSpace(cleanedText)
	
	// Find JSON object using bracket matching
	startIdx := strings.Index(cleanedText, "{")
	if startIdx == -1 {
		return nil, fmt.Errorf("no JSON object found")
	}
	
	bracketCount := 0
	endIdx := startIdx
	
	for i, char := range cleanedText[startIdx:] {
		if char == '{' {
			bracketCount++
		} else if char == '}' {
			bracketCount--
			if bracketCount == 0 {
				endIdx = startIdx + i + 1
				break
			}
		}
	}
	
	if bracketCount != 0 {
		return nil, fmt.Errorf("unmatched brackets in JSON")
	}
	
	jsonStr := cleanedText[startIdx:endIdx]
	
	// Clean up common JSON formatting issues
	jsonStr = regexp.MustCompile(`\s+`).ReplaceAllString(jsonStr, " ")
	jsonStr = regexp.MustCompile(`,\s*}`).ReplaceAllString(jsonStr, "}")
	jsonStr = regexp.MustCompile(`,\s*]`).ReplaceAllString(jsonStr, "]")
	
	var analysis agent.Analysis
	if err := json.Unmarshal([]byte(jsonStr), &analysis); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	return &analysis, nil
}

// createFallbackAnalysis creates a fallback analysis when AI parsing fails
func (p *OpenAICompatibleProvider) createFallbackAnalysis(reason string) *agent.Analysis {
	return &agent.Analysis{
		ErrorType:       "Analysis Parsing Error",
		Severity:        "medium",
		Category:        "infrastructure",
		Description:     fmt.Sprintf("Failed to parse AI analysis response: %s", reason),
		RootCause:       "AI response parsing failure",
		BusinessImpact:  "Analysis quality may be reduced",
		SuggestedAction: "Review AI prompt and response format",
		IsRetryable:     true,
		ComplianceRisk:  "low",
		CustomerImpact:  "No direct customer impact",
		Confidence:      0.5,
		Tags:            []string{"parsing_error", "fallback"},
	}
}

// GetCapabilities returns the capabilities of the AI provider
func (p *OpenAICompatibleProvider) GetCapabilities() map[string]interface{} {
	return map[string]interface{}{
		"type":         "openai_compatible",
		"model":        p.modelName,
		"max_tokens":   p.maxTokens,
		"temperature":  p.temperature,
		"supports_streaming": false,
		"supports_functions": false,
		"api_url":      p.apiURL,
	}
}

// Close closes the AI provider connection
func (p *OpenAICompatibleProvider) Close() error {
	// No persistent connections to close for HTTP-based provider
	return nil
}

// AnthropicProvider implements AIProvider for Anthropic Claude API
type AnthropicProvider struct {
	client      *resty.Client
	apiKey      string
	modelName   string
	maxTokens   int
	temperature float64
}

// NewAnthropicProvider creates a new Anthropic provider
func NewAnthropicProvider() *AnthropicProvider {
	return &AnthropicProvider{}
}

// Initialize initializes the Anthropic provider
func (p *AnthropicProvider) Initialize(config map[string]interface{}) error {
	apiKey, ok := config["api_key"].(string)
	if !ok || apiKey == "" {
		return fmt.Errorf("api_key is required for Anthropic provider")
	}
	p.apiKey = apiKey
	
	if modelName, ok := config["model_name"].(string); ok {
		p.modelName = modelName
	} else {
		p.modelName = "claude-3-sonnet-20240229"
	}
	
	if maxTokens, ok := config["max_tokens"].(int); ok {
		p.maxTokens = maxTokens
	} else {
		p.maxTokens = 2000
	}
	
	if temp, ok := config["temperature"].(float64); ok {
		p.temperature = temp
	} else {
		p.temperature = 0.1
	}
	
	p.client = resty.New()
	if timeoutSeconds, ok := config["timeout_seconds"].(int); ok {
		p.client.SetTimeout(time.Duration(timeoutSeconds) * time.Second)
	} else {
		p.client.SetTimeout(60 * time.Second)
	}
	
	return nil
}

// Analyze performs AI analysis using Anthropic Claude
func (p *AnthropicProvider) Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*agent.Analysis, error) {
	// TODO: Implement Anthropic API call
	return nil, fmt.Errorf("Anthropic provider not implemented yet")
}

// GetCapabilities returns the capabilities of the Anthropic provider
func (p *AnthropicProvider) GetCapabilities() map[string]interface{} {
	return map[string]interface{}{
		"type":         "anthropic",
		"model":        p.modelName,
		"max_tokens":   p.maxTokens,
		"temperature":  p.temperature,
		"supports_streaming": true,
		"supports_functions": false,
	}
}

// Close closes the Anthropic provider connection
func (p *AnthropicProvider) Close() error {
	return nil
}
