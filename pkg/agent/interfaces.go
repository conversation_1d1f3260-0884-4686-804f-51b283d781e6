package agent

import (
	"context"
)

// DataEntry represents a generic data entry to be processed
type DataEntry struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Title    string                 `json:"title,omitempty"`
	Message  string                 `json:"message,omitempty"`
	Entity   string                 `json:"entity,omitempty"`
	Location string                 `json:"location,omitempty"`
	Proto    string                 `json:"proto,omitempty"`
	Data     map[string]interface{} `json:"data,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Analysis represents the result of AI analysis
type Analysis struct {
	ID              string                 `json:"id"`
	ErrorType       string                 `json:"error_type,omitempty"`
	Severity        string                 `json:"severity"`
	Category        string                 `json:"category"`
	Description     string                 `json:"description"`
	RootCause       string                 `json:"root_cause,omitempty"`
	BusinessImpact  string                 `json:"business_impact,omitempty"`
	SuggestedAction string                 `json:"suggested_action,omitempty"`
	IsRetryable     bool                   `json:"is_retryable"`
	ComplianceRisk  string                 `json:"compliance_risk,omitempty"`
	CustomerImpact  string                 `json:"customer_impact,omitempty"`
	Confidence      float64                `json:"confidence,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	CustomFields    map[string]interface{} `json:"custom_fields,omitempty"`
}

// Report represents a complete analysis report
type Report struct {
	Timestamp   string                 `json:"timestamp"`
	AgentInfo   map[string]string      `json:"agent_info"`
	DataEntry   DataEntry              `json:"data_entry"`
	Analysis    Analysis               `json:"analysis"`
	ProcessedBy string                 `json:"processed_by"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// DataSource interface for different data source implementations
type DataSource interface {
	// Initialize the data source
	Initialize(config map[string]interface{}) error
	
	// GetEntries retrieves data entries to be processed
	GetEntries(ctx context.Context) ([]DataEntry, error)
	
	// GetDetailedData retrieves detailed data for a specific entry
	GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error)
	
	// Close closes the data source connection
	Close() error
}

// AIProvider interface for different AI service implementations
type AIProvider interface {
	// Initialize the AI provider
	Initialize(config map[string]interface{}) error
	
	// Analyze performs AI analysis on the given data
	Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*Analysis, error)
	
	// GetCapabilities returns the capabilities of the AI provider
	GetCapabilities() map[string]interface{}
	
	// Close closes the AI provider connection
	Close() error
}

// NotificationHandler interface for different notification implementations
type NotificationHandler interface {
	// Initialize the notification handler
	Initialize(config map[string]interface{}) error
	
	// SendNotification sends a notification
	SendNotification(ctx context.Context, report Report) error
	
	// Close closes the notification handler
	Close() error
}

// OutputHandler interface for different output implementations
type OutputHandler interface {
	// Initialize the output handler
	Initialize(config map[string]interface{}) error
	
	// SaveReport saves the analysis report
	SaveReport(ctx context.Context, report Report) error
	
	// Close closes the output handler
	Close() error
}

// PromptBuilder interface for building AI prompts
type PromptBuilder interface {
	// BuildPrompt builds a prompt for AI analysis
	BuildPrompt(entry DataEntry, detailedData map[string]interface{}, config map[string]interface{}) string
	
	// GetTemplate returns the prompt template
	GetTemplate() string
	
	// SetTemplate sets the prompt template
	SetTemplate(template string)
}

// FailureDetector interface for detecting failures in data
type FailureDetector interface {
	// IsFailure determines if a data entry represents a failure
	IsFailure(entry DataEntry) bool
	
	// GetFailureReason returns the reason why an entry is considered a failure
	GetFailureReason(entry DataEntry) string
	
	// Configure configures the failure detector
	Configure(config map[string]interface{}) error
}

// Agent interface represents the main agent functionality
type Agent interface {
	// Initialize initializes the agent with configuration
	Initialize(configPath string) error
	
	// Start starts the agent processing
	Start(ctx context.Context) error
	
	// Stop stops the agent processing
	Stop() error
	
	// ProcessSingle processes a single data entry
	ProcessSingle(ctx context.Context, entry DataEntry) (*Report, error)
	
	// GetStatus returns the current status of the agent
	GetStatus() map[string]interface{}
	
	// GetConfig returns the agent configuration
	GetConfig() map[string]interface{}
}

// Plugin interface for extending agent functionality
type Plugin interface {
	// GetName returns the plugin name
	GetName() string
	
	// GetVersion returns the plugin version
	GetVersion() string
	
	// Initialize initializes the plugin
	Initialize(config map[string]interface{}) error
	
	// Execute executes the plugin functionality
	Execute(ctx context.Context, data map[string]interface{}) (map[string]interface{}, error)
	
	// Close closes the plugin
	Close() error
}
