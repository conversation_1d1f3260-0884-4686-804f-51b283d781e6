package datasource

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"general-ai-agent-structure/pkg/agent"

	"github.com/sirupsen/logrus"
)

// JSONFileDataSource implements DataSource for JSON file-based data
type JSONFileDataSource struct {
	filePath         string
	format           string
	detailedDataPath string
	dataIndex        *DataIndex
	fieldMappings    map[string]string
	customParser     func(map[string]interface{}) agent.DataEntry
}

// DataIndex represents a generic data index structure
type DataIndex struct {
	Page       int               `json:"page,omitempty"`
	PerPage    int               `json:"per_page,omitempty"`
	Total      int               `json:"total,omitempty"`
	TotalPages int               `json:"total_pages,omitempty"`
	Items      []agent.DataEntry `json:"items,omitempty"`
	Rows       []agent.DataEntry `json:"rows,omitempty"`    // Legacy support
	Data       []agent.DataEntry `json:"data,omitempty"`    // Alternative field
	Records    []agent.DataEntry `json:"records,omitempty"` // Alternative field
	Entries    []agent.DataEntry `json:"entries,omitempty"` // Alternative field
	Events     []agent.DataEntry `json:"events,omitempty"`  // Alternative field
}

// NewJSONFileDataSource creates a new JSON file data source
func NewJSONFileDataSource() *JSONFileDataSource {
	return &JSONFileDataSource{}
}

// Initialize initializes the JSON file data source
func (ds *JSONFileDataSource) Initialize(config map[string]interface{}) error {
	// Extract configuration
	path, ok := config["path"].(string)
	if !ok || path == "" {
		return fmt.Errorf("path is required for JSON file data source")
	}
	ds.filePath = path

	if format, ok := config["format"].(string); ok {
		ds.format = format
	} else {
		ds.format = "auto_detect" // default format
	}

	if detailedPath, ok := config["detailed_data_path"].(string); ok {
		ds.detailedDataPath = detailedPath
	}

	// Initialize field mappings
	if mappings, ok := config["field_mappings"].(map[string]interface{}); ok {
		ds.fieldMappings = make(map[string]string)
		for key, value := range mappings {
			if strValue, ok := value.(string); ok {
				ds.fieldMappings[key] = strValue
			}
		}
	} else {
		// Default field mappings
		ds.fieldMappings = map[string]string{
			"id":       "id",
			"type":     "type",
			"title":    "title",
			"message":  "message",
			"entity":   "entity",
			"location": "location",
			"proto":    "proto",
		}
	}

	// Validate file exists
	if _, err := os.Stat(ds.filePath); os.IsNotExist(err) {
		return fmt.Errorf("data file does not exist: %s", ds.filePath)
	}

	return nil
}

// GetEntries retrieves data entries from the JSON file
func (ds *JSONFileDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	data, err := os.ReadFile(ds.filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	switch ds.format {
	case "auto_detect":
		return ds.parseAutoDetect(data)
	case "index_format":
		return ds.parseIndexFormat(data)
	case "simple_array":
		return ds.parseSimpleArray(data)
	case "custom":
		return ds.parseCustomFormat(data)
	case "log_index": // Legacy support
		return ds.parseIndexFormat(data)
	default:
		return ds.parseAutoDetect(data)
	}
}

// parseAutoDetect automatically detects the JSON format and parses accordingly
func (ds *JSONFileDataSource) parseAutoDetect(data []byte) ([]agent.DataEntry, error) {
	// First try to parse as raw JSON to determine structure
	var rawData interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	switch v := rawData.(type) {
	case []interface{}:
		// Direct array format
		return ds.parseDirectArray(data)
	case map[string]interface{}:
		// Object format - check for known array fields
		if ds.hasArrayField(v, []string{"items", "data", "records", "entries", "events", "rows"}) {
			return ds.parseIndexFormat(data)
		}
		// Single object - wrap in array
		return ds.parseSingleObject(v)
	default:
		return nil, fmt.Errorf("unsupported JSON structure")
	}
}

// hasArrayField checks if the object has any of the specified array fields
func (ds *JSONFileDataSource) hasArrayField(obj map[string]interface{}, fields []string) bool {
	for _, field := range fields {
		if val, exists := obj[field]; exists {
			if _, isArray := val.([]interface{}); isArray {
				return true
			}
		}
	}
	return false
}

// parseIndexFormat parses index-style format (with metadata)
func (ds *JSONFileDataSource) parseIndexFormat(data []byte) ([]agent.DataEntry, error) {
	var dataIndex DataIndex
	if err := json.Unmarshal(data, &dataIndex); err != nil {
		return nil, fmt.Errorf("failed to parse index format: %w", err)
	}

	ds.dataIndex = &dataIndex

	// Try different array fields in order of preference
	if len(dataIndex.Items) > 0 {
		return dataIndex.Items, nil
	}
	if len(dataIndex.Data) > 0 {
		return dataIndex.Data, nil
	}
	if len(dataIndex.Records) > 0 {
		return dataIndex.Records, nil
	}
	if len(dataIndex.Entries) > 0 {
		return dataIndex.Entries, nil
	}
	if len(dataIndex.Events) > 0 {
		return dataIndex.Events, nil
	}
	if len(dataIndex.Rows) > 0 {
		return dataIndex.Rows, nil
	}

	return []agent.DataEntry{}, nil
}

// parseDirectArray parses direct array format
func (ds *JSONFileDataSource) parseDirectArray(data []byte) ([]agent.DataEntry, error) {
	var rawArray []map[string]interface{}
	if err := json.Unmarshal(data, &rawArray); err != nil {
		return nil, fmt.Errorf("failed to parse array: %w", err)
	}

	var entries []agent.DataEntry
	for i, item := range rawArray {
		entry := ds.convertToDataEntry(item, fmt.Sprintf("item_%d", i))
		entries = append(entries, entry)
	}

	return entries, nil
}

// parseSingleObject parses single object format
func (ds *JSONFileDataSource) parseSingleObject(obj map[string]interface{}) ([]agent.DataEntry, error) {
	entry := ds.convertToDataEntry(obj, "single_object")
	return []agent.DataEntry{entry}, nil
}

// convertToDataEntry converts a generic map to DataEntry using field mappings
func (ds *JSONFileDataSource) convertToDataEntry(data map[string]interface{}, defaultID string) agent.DataEntry {
	// Use custom parser if available
	if ds.customParser != nil {
		return ds.customParser(data)
	}

	// Generate ID
	id := ds.getStringValue(data, ds.fieldMappings["id"], defaultID)
	if id == defaultID && defaultID == "" {
		id = fmt.Sprintf("entry_%d", time.Now().UnixNano())
	}

	entry := agent.DataEntry{
		ID:       id,
		Type:     ds.getStringValue(data, ds.fieldMappings["type"], "unknown"),
		Title:    ds.getStringValue(data, ds.fieldMappings["title"], ""),
		Message:  ds.getStringValue(data, ds.fieldMappings["message"], ""),
		Entity:   ds.getStringValue(data, ds.fieldMappings["entity"], ""),
		Location: ds.getStringValue(data, ds.fieldMappings["location"], ""),
		Proto:    ds.getStringValue(data, ds.fieldMappings["proto"], ""),
		Data:     data,
		Metadata: map[string]interface{}{
			"source":    "json_file",
			"file_path": ds.filePath,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	// If no message, try common alternatives
	if entry.Message == "" {
		alternatives := []string{"description", "text", "content", "body", "summary"}
		for _, alt := range alternatives {
			if val := ds.getStringValue(data, alt, ""); val != "" {
				entry.Message = val
				break
			}
		}
	}

	// If no title, try common alternatives
	if entry.Title == "" {
		alternatives := []string{"name", "subject", "event", "event_type", "alert_name"}
		for _, alt := range alternatives {
			if val := ds.getStringValue(data, alt, ""); val != "" {
				entry.Title = val
				break
			}
		}
	}

	return entry
}

// getStringValue safely extracts string value with fallback
func (ds *JSONFileDataSource) getStringValue(data map[string]interface{}, key, fallback string) string {
	if key == "" {
		return fallback
	}

	if val, ok := data[key]; ok {
		switch v := val.(type) {
		case string:
			return v
		case int, int64, float64:
			return fmt.Sprintf("%v", v)
		case bool:
			return fmt.Sprintf("%t", v)
		default:
			// Try to convert to JSON string for complex types
			if jsonBytes, err := json.Marshal(v); err == nil {
				return string(jsonBytes)
			}
		}
	}

	return fallback
}

// parseSimpleArray parses simple array format
func (ds *JSONFileDataSource) parseSimpleArray(data []byte) ([]agent.DataEntry, error) {
	// Try to parse as DataEntry array first
	var entries []agent.DataEntry
	if err := json.Unmarshal(data, &entries); err == nil {
		return entries, nil
	}

	// If that fails, parse as generic array and convert
	var rawArray []map[string]interface{}
	if err := json.Unmarshal(data, &rawArray); err != nil {
		return nil, fmt.Errorf("failed to parse simple array: %w", err)
	}

	var convertedEntries []agent.DataEntry
	for i, item := range rawArray {
		entry := ds.convertToDataEntry(item, fmt.Sprintf("array_item_%d", i))
		convertedEntries = append(convertedEntries, entry)
	}

	return convertedEntries, nil
}

// parseCustomFormat parses custom format using field mappings
func (ds *JSONFileDataSource) parseCustomFormat(data []byte) ([]agent.DataEntry, error) {
	var rawData map[string]interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		return nil, fmt.Errorf("failed to parse custom format: %w", err)
	}

	var entries []agent.DataEntry

	// Look for arrays in the data using common field names
	arrayFields := []string{"events", "alerts", "notifications", "messages", "items", "data", "records"}

	for _, field := range arrayFields {
		if arrayData, ok := rawData[field].([]interface{}); ok {
			for i, item := range arrayData {
				if itemMap, ok := item.(map[string]interface{}); ok {
					entry := ds.convertToDataEntry(itemMap, fmt.Sprintf("%s_%d", field, i))
					entries = append(entries, entry)
				}
			}
			break // Use first found array
		}
	}

	// If no arrays found, treat the whole object as a single entry
	if len(entries) == 0 {
		entry := ds.convertToDataEntry(rawData, "custom_entry")
		entries = append(entries, entry)
	}

	return entries, nil
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *JSONFileDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	if ds.detailedDataPath == "" {
		return make(map[string]interface{}), nil
	}

	// Find the entry index
	entryIndex := -1
	if ds.dataIndex != nil {
		// Try all possible array fields
		allEntries := []agent.DataEntry{}
		allEntries = append(allEntries, ds.dataIndex.Items...)
		allEntries = append(allEntries, ds.dataIndex.Data...)
		allEntries = append(allEntries, ds.dataIndex.Records...)
		allEntries = append(allEntries, ds.dataIndex.Entries...)
		allEntries = append(allEntries, ds.dataIndex.Events...)
		allEntries = append(allEntries, ds.dataIndex.Rows...)

		for i, entry := range allEntries {
			if entry.ID == entryID {
				entryIndex = i
				break
			}
		}
	}

	if entryIndex == -1 {
		return make(map[string]interface{}), nil
	}

	// Try different file naming patterns
	possibleFiles := []string{
		filepath.Join(ds.detailedDataPath, fmt.Sprintf("%d.json", entryIndex+1)), // 1-indexed
		filepath.Join(ds.detailedDataPath, fmt.Sprintf("%d.json", entryIndex)),   // 0-indexed
		filepath.Join(ds.detailedDataPath, fmt.Sprintf("%s.json", entryID)),      // ID-based
		filepath.Join(ds.detailedDataPath, entryID+".json"),                      // ID with extension
	}

	for _, filename := range possibleFiles {
		if _, err := os.Stat(filename); err == nil {
			data, err := os.ReadFile(filename)
			if err != nil {
				continue
			}

			var detailedData map[string]interface{}
			if err := json.Unmarshal(data, &detailedData); err != nil {
				continue
			}

			return detailedData, nil
		}
	}

	// If no detailed file found, return empty map
	return make(map[string]interface{}), nil
}

// Close closes the data source
func (ds *JSONFileDataSource) Close() error {
	// No resources to close for file-based data source
	return nil
}

// DatabaseDataSource implements DataSource for database-based data
type DatabaseDataSource struct {
	connectionString string
	query            string
	// Add database connection fields as needed
}

// NewDatabaseDataSource creates a new database data source
func NewDatabaseDataSource() *DatabaseDataSource {
	return &DatabaseDataSource{}
}

// Initialize initializes the database data source
func (ds *DatabaseDataSource) Initialize(config map[string]interface{}) error {
	connStr, ok := config["connection_string"].(string)
	if !ok || connStr == "" {
		return fmt.Errorf("connection_string is required for database data source")
	}
	ds.connectionString = connStr

	if query, ok := config["query"].(string); ok {
		ds.query = query
	}

	// TODO: Initialize database connection
	return fmt.Errorf("database data source not implemented yet")
}

// GetEntries retrieves data entries from the database
func (ds *DatabaseDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	// TODO: Implement database query
	return nil, fmt.Errorf("database data source not implemented yet")
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *DatabaseDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	// TODO: Implement database query for detailed data
	return nil, fmt.Errorf("database data source not implemented yet")
}

// Close closes the database connection
func (ds *DatabaseDataSource) Close() error {
	// TODO: Close database connection
	return nil
}

// APIDataSource implements DataSource for API-based data
type APIDataSource struct {
	baseURL string
	apiKey  string
	// Add HTTP client and other fields as needed
}

// NewAPIDataSource creates a new API data source
func NewAPIDataSource() *APIDataSource {
	return &APIDataSource{}
}

// Initialize initializes the API data source
func (ds *APIDataSource) Initialize(config map[string]interface{}) error {
	baseURL, ok := config["base_url"].(string)
	if !ok || baseURL == "" {
		return fmt.Errorf("base_url is required for API data source")
	}
	ds.baseURL = baseURL

	if apiKey, ok := config["api_key"].(string); ok {
		ds.apiKey = apiKey
	}

	// TODO: Initialize HTTP client
	return fmt.Errorf("API data source not implemented yet")
}

// GetEntries retrieves data entries from the API
func (ds *APIDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	// TODO: Implement API call
	return nil, fmt.Errorf("API data source not implemented yet")
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *APIDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	// TODO: Implement API call for detailed data
	return nil, fmt.Errorf("API data source not implemented yet")
}

// Close closes the API data source
func (ds *APIDataSource) Close() error {
	// TODO: Close HTTP client
	return nil
}

// WebhookDataSource implements DataSource for webhook-based real-time data
type WebhookDataSource struct {
	enabled      bool
	port         int
	endpoint     string
	server       *http.Server
	entryQueue   chan agent.DataEntry
	detailedData map[string]map[string]interface{}
	mu           sync.RWMutex
	logger       *logrus.Logger
}

// NewWebhookDataSource creates a new webhook data source
func NewWebhookDataSource() *WebhookDataSource {
	logger := logrus.New()
	return &WebhookDataSource{
		entryQueue:   make(chan agent.DataEntry, 1000), // Buffer for 1000 entries
		detailedData: make(map[string]map[string]interface{}),
		logger:       logger,
	}
}

// Initialize initializes the webhook data source
func (ds *WebhookDataSource) Initialize(config map[string]interface{}) error {
	// Extract configuration
	if enabled, ok := config["enabled"].(bool); ok {
		ds.enabled = enabled
	} else {
		ds.enabled = true
	}

	if !ds.enabled {
		return nil
	}

	if port, ok := config["port"].(int); ok {
		ds.port = port
	} else {
		ds.port = 8080 // default port
	}

	if endpoint, ok := config["endpoint"].(string); ok {
		ds.endpoint = endpoint
	} else {
		ds.endpoint = "/webhook" // default endpoint
	}

	// Setup HTTP server
	mux := http.NewServeMux()
	mux.HandleFunc(ds.endpoint, ds.handleWebhook)
	mux.HandleFunc("/health", ds.handleHealth)

	ds.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", ds.port),
		Handler: mux,
	}

	// Start server in goroutine
	go func() {
		ds.logger.Infof("🌐 Webhook server starting on port %d, endpoint %s", ds.port, ds.endpoint)
		if err := ds.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ds.logger.Errorf("Webhook server error: %v", err)
		}
	}()

	return nil
}

// handleWebhook handles incoming webhook requests
func (ds *WebhookDataSource) handleWebhook(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		ds.logger.Errorf("Failed to read webhook body: %v", err)
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// Parse JSON
	var webhookData map[string]interface{}
	if err := json.Unmarshal(body, &webhookData); err != nil {
		ds.logger.Errorf("Failed to parse webhook JSON: %v", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Convert to DataEntry
	entry := ds.convertToDataEntry(webhookData, r.Header)

	// Store detailed data
	ds.mu.Lock()
	ds.detailedData[entry.ID] = webhookData
	ds.mu.Unlock()

	// Add to queue (non-blocking)
	select {
	case ds.entryQueue <- entry:
		ds.logger.Infof("📨 Webhook data received: %s", entry.ID)
	default:
		ds.logger.Warnf("⚠️ Webhook queue full, dropping entry: %s", entry.ID)
	}

	// Respond with success
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":   "success",
		"message":  "Webhook received",
		"entry_id": entry.ID,
	})
}

// convertToDataEntry converts webhook data to DataEntry
func (ds *WebhookDataSource) convertToDataEntry(data map[string]interface{}, headers http.Header) agent.DataEntry {
	// Generate unique ID
	entryID := fmt.Sprintf("webhook_%d", time.Now().UnixNano())

	// Extract common fields with fallbacks
	entry := agent.DataEntry{
		ID:       entryID,
		Type:     ds.getStringValue(data, "type", "webhook"),
		Title:    ds.getStringValue(data, "title", "Webhook Event"),
		Message:  ds.getStringValue(data, "message", ""),
		Entity:   ds.getStringValue(data, "entity", "webhook"),
		Location: ds.getStringValue(data, "location", ""),
		Proto:    ds.getStringValue(data, "proto", "HTTP"),
		Data:     data,
		Metadata: map[string]interface{}{
			"timestamp":    time.Now().Format(time.RFC3339),
			"source":       "webhook",
			"content_type": headers.Get("Content-Type"),
			"user_agent":   headers.Get("User-Agent"),
			"remote_addr":  headers.Get("X-Forwarded-For"),
		},
	}

	// If no message, try to extract from common fields
	if entry.Message == "" {
		if desc, ok := data["description"].(string); ok {
			entry.Message = desc
		} else if event, ok := data["event"].(string); ok {
			entry.Message = event
		} else if text, ok := data["text"].(string); ok {
			entry.Message = text
		} else {
			entry.Message = "Webhook event received"
		}
	}

	return entry
}

// getStringValue safely extracts string value with fallback
func (ds *WebhookDataSource) getStringValue(data map[string]interface{}, key, fallback string) string {
	if val, ok := data[key].(string); ok {
		return val
	}
	return fallback
}

// handleHealth handles health check requests
func (ds *WebhookDataSource) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":     "healthy",
		"queue_size": len(ds.entryQueue),
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// GetEntries retrieves data entries from the webhook queue
func (ds *WebhookDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	if !ds.enabled {
		return []agent.DataEntry{}, nil
	}

	var entries []agent.DataEntry

	// Collect all available entries from queue (non-blocking)
	for {
		select {
		case entry := <-ds.entryQueue:
			entries = append(entries, entry)
		default:
			// No more entries available
			return entries, nil
		}
	}
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *WebhookDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	if !ds.enabled {
		return make(map[string]interface{}), nil
	}

	ds.mu.RLock()
	defer ds.mu.RUnlock()

	if detailedData, exists := ds.detailedData[entryID]; exists {
		return detailedData, nil
	}

	return make(map[string]interface{}), nil
}

// Close closes the webhook data source
func (ds *WebhookDataSource) Close() error {
	if ds.server != nil {
		ds.logger.Info("🛑 Shutting down webhook server...")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := ds.server.Shutdown(ctx); err != nil {
			ds.logger.Errorf("Error shutting down webhook server: %v", err)
			return err
		}
	}

	// Close the entry queue
	close(ds.entryQueue)

	ds.logger.Info("✅ Webhook data source closed successfully")
	return nil
}
