package datasource

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"general-ai-agent-structure/pkg/agent"

	"github.com/sirupsen/logrus"
)

// JSONFileDataSource implements DataSource for JSON file-based data
type JSONFileDataSource struct {
	filePath         string
	format           string
	detailedLogsPath string
	logIndex         *LogIndex
}

// LogIndex represents the structure of a log index file
type LogIndex struct {
	Page       int               `json:"page"`
	PerPage    int               `json:"per_page"`
	Total      int               `json:"total"`
	TotalPages int               `json:"total_pages"`
	Rows       []agent.DataEntry `json:"rows"`
}

// NewJSONFileDataSource creates a new JSON file data source
func NewJSONFileDataSource() *JSONFileDataSource {
	return &JSONFileDataSource{}
}

// Initialize initializes the JSON file data source
func (ds *JSONFileDataSource) Initialize(config map[string]interface{}) error {
	// Extract configuration
	path, ok := config["path"].(string)
	if !ok || path == "" {
		return fmt.Errorf("path is required for JSON file data source")
	}
	ds.filePath = path

	if format, ok := config["format"].(string); ok {
		ds.format = format
	} else {
		ds.format = "log_index" // default format
	}

	if detailedPath, ok := config["detailed_logs_path"].(string); ok {
		ds.detailedLogsPath = detailedPath
	}

	// Validate file exists
	if _, err := os.Stat(ds.filePath); os.IsNotExist(err) {
		return fmt.Errorf("data file does not exist: %s", ds.filePath)
	}

	return nil
}

// GetEntries retrieves data entries from the JSON file
func (ds *JSONFileDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	data, err := os.ReadFile(ds.filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	switch ds.format {
	case "log_index":
		return ds.parseLogIndex(data)
	case "simple_array":
		return ds.parseSimpleArray(data)
	case "custom":
		return ds.parseCustomFormat(data)
	default:
		return nil, fmt.Errorf("unsupported format: %s", ds.format)
	}
}

// parseLogIndex parses log index format (original format)
func (ds *JSONFileDataSource) parseLogIndex(data []byte) ([]agent.DataEntry, error) {
	var logIndex LogIndex
	if err := json.Unmarshal(data, &logIndex); err != nil {
		return nil, fmt.Errorf("failed to parse log index: %w", err)
	}

	ds.logIndex = &logIndex
	return logIndex.Rows, nil
}

// parseSimpleArray parses simple array format
func (ds *JSONFileDataSource) parseSimpleArray(data []byte) ([]agent.DataEntry, error) {
	var entries []agent.DataEntry
	if err := json.Unmarshal(data, &entries); err != nil {
		return nil, fmt.Errorf("failed to parse simple array: %w", err)
	}

	return entries, nil
}

// parseCustomFormat parses custom format (can be extended)
func (ds *JSONFileDataSource) parseCustomFormat(data []byte) ([]agent.DataEntry, error) {
	// This can be extended for custom JSON structures
	var rawData map[string]interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		return nil, fmt.Errorf("failed to parse custom format: %w", err)
	}

	// Convert to DataEntry format
	var entries []agent.DataEntry

	// Example: if data has "events" array
	if events, ok := rawData["events"].([]interface{}); ok {
		for i, event := range events {
			if eventMap, ok := event.(map[string]interface{}); ok {
				entry := agent.DataEntry{
					ID:       fmt.Sprintf("event_%d", i),
					Type:     getStringValue(eventMap, "type"),
					Title:    getStringValue(eventMap, "title"),
					Message:  getStringValue(eventMap, "message"),
					Entity:   getStringValue(eventMap, "entity"),
					Location: getStringValue(eventMap, "location"),
					Proto:    getStringValue(eventMap, "proto"),
					Data:     eventMap,
				}
				entries = append(entries, entry)
			}
		}
	}

	return entries, nil
}

// getStringValue safely extracts string value from map
func getStringValue(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *JSONFileDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	if ds.detailedLogsPath == "" {
		return make(map[string]interface{}), nil
	}

	// Find the entry index
	entryIndex := -1
	if ds.logIndex != nil {
		for i, entry := range ds.logIndex.Rows {
			if entry.ID == entryID {
				entryIndex = i
				break
			}
		}
	}

	if entryIndex == -1 {
		return make(map[string]interface{}), nil
	}

	// Load detailed log file (1-indexed)
	filename := filepath.Join(ds.detailedLogsPath, fmt.Sprintf("%d.json", entryIndex+1))

	// Check if file exists
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return make(map[string]interface{}), nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read detailed log file: %w", err)
	}

	var detailedData map[string]interface{}
	if err := json.Unmarshal(data, &detailedData); err != nil {
		return nil, fmt.Errorf("failed to parse detailed log: %w", err)
	}

	return detailedData, nil
}

// Close closes the data source
func (ds *JSONFileDataSource) Close() error {
	// No resources to close for file-based data source
	return nil
}

// DatabaseDataSource implements DataSource for database-based data
type DatabaseDataSource struct {
	connectionString string
	query            string
	// Add database connection fields as needed
}

// NewDatabaseDataSource creates a new database data source
func NewDatabaseDataSource() *DatabaseDataSource {
	return &DatabaseDataSource{}
}

// Initialize initializes the database data source
func (ds *DatabaseDataSource) Initialize(config map[string]interface{}) error {
	connStr, ok := config["connection_string"].(string)
	if !ok || connStr == "" {
		return fmt.Errorf("connection_string is required for database data source")
	}
	ds.connectionString = connStr

	if query, ok := config["query"].(string); ok {
		ds.query = query
	}

	// TODO: Initialize database connection
	return fmt.Errorf("database data source not implemented yet")
}

// GetEntries retrieves data entries from the database
func (ds *DatabaseDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	// TODO: Implement database query
	return nil, fmt.Errorf("database data source not implemented yet")
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *DatabaseDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	// TODO: Implement database query for detailed data
	return nil, fmt.Errorf("database data source not implemented yet")
}

// Close closes the database connection
func (ds *DatabaseDataSource) Close() error {
	// TODO: Close database connection
	return nil
}

// APIDataSource implements DataSource for API-based data
type APIDataSource struct {
	baseURL string
	apiKey  string
	// Add HTTP client and other fields as needed
}

// NewAPIDataSource creates a new API data source
func NewAPIDataSource() *APIDataSource {
	return &APIDataSource{}
}

// Initialize initializes the API data source
func (ds *APIDataSource) Initialize(config map[string]interface{}) error {
	baseURL, ok := config["base_url"].(string)
	if !ok || baseURL == "" {
		return fmt.Errorf("base_url is required for API data source")
	}
	ds.baseURL = baseURL

	if apiKey, ok := config["api_key"].(string); ok {
		ds.apiKey = apiKey
	}

	// TODO: Initialize HTTP client
	return fmt.Errorf("API data source not implemented yet")
}

// GetEntries retrieves data entries from the API
func (ds *APIDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	// TODO: Implement API call
	return nil, fmt.Errorf("API data source not implemented yet")
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *APIDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	// TODO: Implement API call for detailed data
	return nil, fmt.Errorf("API data source not implemented yet")
}

// Close closes the API data source
func (ds *APIDataSource) Close() error {
	// TODO: Close HTTP client
	return nil
}

// WebhookDataSource implements DataSource for webhook-based real-time data
type WebhookDataSource struct {
	enabled      bool
	port         int
	endpoint     string
	server       *http.Server
	entryQueue   chan agent.DataEntry
	detailedData map[string]map[string]interface{}
	mu           sync.RWMutex
	logger       *logrus.Logger
}

// NewWebhookDataSource creates a new webhook data source
func NewWebhookDataSource() *WebhookDataSource {
	logger := logrus.New()
	return &WebhookDataSource{
		entryQueue:   make(chan agent.DataEntry, 1000), // Buffer for 1000 entries
		detailedData: make(map[string]map[string]interface{}),
		logger:       logger,
	}
}

// Initialize initializes the webhook data source
func (ds *WebhookDataSource) Initialize(config map[string]interface{}) error {
	// Extract configuration
	if enabled, ok := config["enabled"].(bool); ok {
		ds.enabled = enabled
	} else {
		ds.enabled = true
	}

	if !ds.enabled {
		return nil
	}

	if port, ok := config["port"].(int); ok {
		ds.port = port
	} else {
		ds.port = 8080 // default port
	}

	if endpoint, ok := config["endpoint"].(string); ok {
		ds.endpoint = endpoint
	} else {
		ds.endpoint = "/webhook" // default endpoint
	}

	// Setup HTTP server
	mux := http.NewServeMux()
	mux.HandleFunc(ds.endpoint, ds.handleWebhook)
	mux.HandleFunc("/health", ds.handleHealth)

	ds.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", ds.port),
		Handler: mux,
	}

	// Start server in goroutine
	go func() {
		ds.logger.Infof("🌐 Webhook server starting on port %d, endpoint %s", ds.port, ds.endpoint)
		if err := ds.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ds.logger.Errorf("Webhook server error: %v", err)
		}
	}()

	return nil
}

// handleWebhook handles incoming webhook requests
func (ds *WebhookDataSource) handleWebhook(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		ds.logger.Errorf("Failed to read webhook body: %v", err)
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// Parse JSON
	var webhookData map[string]interface{}
	if err := json.Unmarshal(body, &webhookData); err != nil {
		ds.logger.Errorf("Failed to parse webhook JSON: %v", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Convert to DataEntry
	entry := ds.convertToDataEntry(webhookData, r.Header)

	// Store detailed data
	ds.mu.Lock()
	ds.detailedData[entry.ID] = webhookData
	ds.mu.Unlock()

	// Add to queue (non-blocking)
	select {
	case ds.entryQueue <- entry:
		ds.logger.Infof("📨 Webhook data received: %s", entry.ID)
	default:
		ds.logger.Warnf("⚠️ Webhook queue full, dropping entry: %s", entry.ID)
	}

	// Respond with success
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":   "success",
		"message":  "Webhook received",
		"entry_id": entry.ID,
	})
}

// convertToDataEntry converts webhook data to DataEntry
func (ds *WebhookDataSource) convertToDataEntry(data map[string]interface{}, headers http.Header) agent.DataEntry {
	// Generate unique ID
	entryID := fmt.Sprintf("webhook_%d", time.Now().UnixNano())

	// Extract common fields with fallbacks
	entry := agent.DataEntry{
		ID:       entryID,
		Type:     ds.getStringValue(data, "type", "webhook"),
		Title:    ds.getStringValue(data, "title", "Webhook Event"),
		Message:  ds.getStringValue(data, "message", ""),
		Entity:   ds.getStringValue(data, "entity", "webhook"),
		Location: ds.getStringValue(data, "location", ""),
		Proto:    ds.getStringValue(data, "proto", "HTTP"),
		Data:     data,
		Metadata: map[string]interface{}{
			"timestamp":    time.Now().Format(time.RFC3339),
			"source":       "webhook",
			"content_type": headers.Get("Content-Type"),
			"user_agent":   headers.Get("User-Agent"),
			"remote_addr":  headers.Get("X-Forwarded-For"),
		},
	}

	// If no message, try to extract from common fields
	if entry.Message == "" {
		if desc, ok := data["description"].(string); ok {
			entry.Message = desc
		} else if event, ok := data["event"].(string); ok {
			entry.Message = event
		} else if text, ok := data["text"].(string); ok {
			entry.Message = text
		} else {
			entry.Message = "Webhook event received"
		}
	}

	return entry
}

// getStringValue safely extracts string value with fallback
func (ds *WebhookDataSource) getStringValue(data map[string]interface{}, key, fallback string) string {
	if val, ok := data[key].(string); ok {
		return val
	}
	return fallback
}

// handleHealth handles health check requests
func (ds *WebhookDataSource) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":     "healthy",
		"queue_size": len(ds.entryQueue),
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// GetEntries retrieves data entries from the webhook queue
func (ds *WebhookDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	if !ds.enabled {
		return []agent.DataEntry{}, nil
	}

	var entries []agent.DataEntry

	// Collect all available entries from queue (non-blocking)
	for {
		select {
		case entry := <-ds.entryQueue:
			entries = append(entries, entry)
		default:
			// No more entries available
			return entries, nil
		}
	}
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *WebhookDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	if !ds.enabled {
		return make(map[string]interface{}), nil
	}

	ds.mu.RLock()
	defer ds.mu.RUnlock()

	if detailedData, exists := ds.detailedData[entryID]; exists {
		return detailedData, nil
	}

	return make(map[string]interface{}), nil
}

// Close closes the webhook data source
func (ds *WebhookDataSource) Close() error {
	if ds.server != nil {
		ds.logger.Info("🛑 Shutting down webhook server...")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := ds.server.Shutdown(ctx); err != nil {
			ds.logger.Errorf("Error shutting down webhook server: %v", err)
			return err
		}
	}

	// Close the entry queue
	close(ds.entryQueue)

	ds.logger.Info("✅ Webhook data source closed successfully")
	return nil
}
