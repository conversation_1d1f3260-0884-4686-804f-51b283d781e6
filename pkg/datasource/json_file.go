package datasource

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"general-ai-agent-structure/pkg/agent"
)

// JSONFileDataSource implements DataSource for JSON file-based data
type JSONFileDataSource struct {
	filePath         string
	format           string
	detailedLogsPath string
	logIndex         *LogIndex
}

// LogIndex represents the structure of a log index file
type LogIndex struct {
	Page       int               `json:"page"`
	PerPage    int               `json:"per_page"`
	Total      int               `json:"total"`
	TotalPages int               `json:"total_pages"`
	Rows       []agent.DataEntry `json:"rows"`
}

// NewJSONFileDataSource creates a new JSON file data source
func NewJSONFileDataSource() *JSONFileDataSource {
	return &JSONFileDataSource{}
}

// Initialize initializes the JSON file data source
func (ds *JSONFileDataSource) Initialize(config map[string]interface{}) error {
	// Extract configuration
	path, ok := config["path"].(string)
	if !ok || path == "" {
		return fmt.Errorf("path is required for JSON file data source")
	}
	ds.filePath = path

	if format, ok := config["format"].(string); ok {
		ds.format = format
	} else {
		ds.format = "log_index" // default format
	}

	if detailedPath, ok := config["detailed_logs_path"].(string); ok {
		ds.detailedLogsPath = detailedPath
	}

	// Validate file exists
	if _, err := os.Stat(ds.filePath); os.IsNotExist(err) {
		return fmt.Errorf("data file does not exist: %s", ds.filePath)
	}

	return nil
}

// GetEntries retrieves data entries from the JSON file
func (ds *JSONFileDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	data, err := os.ReadFile(ds.filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	switch ds.format {
	case "log_index":
		return ds.parseLogIndex(data)
	case "simple_array":
		return ds.parseSimpleArray(data)
	case "custom":
		return ds.parseCustomFormat(data)
	default:
		return nil, fmt.Errorf("unsupported format: %s", ds.format)
	}
}

// parseLogIndex parses log index format (original format)
func (ds *JSONFileDataSource) parseLogIndex(data []byte) ([]agent.DataEntry, error) {
	var logIndex LogIndex
	if err := json.Unmarshal(data, &logIndex); err != nil {
		return nil, fmt.Errorf("failed to parse log index: %w", err)
	}

	ds.logIndex = &logIndex
	return logIndex.Rows, nil
}

// parseSimpleArray parses simple array format
func (ds *JSONFileDataSource) parseSimpleArray(data []byte) ([]agent.DataEntry, error) {
	var entries []agent.DataEntry
	if err := json.Unmarshal(data, &entries); err != nil {
		return nil, fmt.Errorf("failed to parse simple array: %w", err)
	}

	return entries, nil
}

// parseCustomFormat parses custom format (can be extended)
func (ds *JSONFileDataSource) parseCustomFormat(data []byte) ([]agent.DataEntry, error) {
	// This can be extended for custom JSON structures
	var rawData map[string]interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		return nil, fmt.Errorf("failed to parse custom format: %w", err)
	}

	// Convert to DataEntry format
	var entries []agent.DataEntry

	// Example: if data has "events" array
	if events, ok := rawData["events"].([]interface{}); ok {
		for i, event := range events {
			if eventMap, ok := event.(map[string]interface{}); ok {
				entry := agent.DataEntry{
					ID:       fmt.Sprintf("event_%d", i),
					Type:     getStringValue(eventMap, "type"),
					Title:    getStringValue(eventMap, "title"),
					Message:  getStringValue(eventMap, "message"),
					Entity:   getStringValue(eventMap, "entity"),
					Location: getStringValue(eventMap, "location"),
					Proto:    getStringValue(eventMap, "proto"),
					Data:     eventMap,
				}
				entries = append(entries, entry)
			}
		}
	}

	return entries, nil
}

// getStringValue safely extracts string value from map
func getStringValue(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *JSONFileDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	if ds.detailedLogsPath == "" {
		return make(map[string]interface{}), nil
	}

	// Find the entry index
	entryIndex := -1
	if ds.logIndex != nil {
		for i, entry := range ds.logIndex.Rows {
			if entry.ID == entryID {
				entryIndex = i
				break
			}
		}
	}

	if entryIndex == -1 {
		return make(map[string]interface{}), nil
	}

	// Load detailed log file (1-indexed)
	filename := filepath.Join(ds.detailedLogsPath, fmt.Sprintf("%d.json", entryIndex+1))

	// Check if file exists
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return make(map[string]interface{}), nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read detailed log file: %w", err)
	}

	var detailedData map[string]interface{}
	if err := json.Unmarshal(data, &detailedData); err != nil {
		return nil, fmt.Errorf("failed to parse detailed log: %w", err)
	}

	return detailedData, nil
}

// Close closes the data source
func (ds *JSONFileDataSource) Close() error {
	// No resources to close for file-based data source
	return nil
}

// DatabaseDataSource implements DataSource for database-based data
type DatabaseDataSource struct {
	connectionString string
	query            string
	// Add database connection fields as needed
}

// NewDatabaseDataSource creates a new database data source
func NewDatabaseDataSource() *DatabaseDataSource {
	return &DatabaseDataSource{}
}

// Initialize initializes the database data source
func (ds *DatabaseDataSource) Initialize(config map[string]interface{}) error {
	connStr, ok := config["connection_string"].(string)
	if !ok || connStr == "" {
		return fmt.Errorf("connection_string is required for database data source")
	}
	ds.connectionString = connStr

	if query, ok := config["query"].(string); ok {
		ds.query = query
	}

	// TODO: Initialize database connection
	return fmt.Errorf("database data source not implemented yet")
}

// GetEntries retrieves data entries from the database
func (ds *DatabaseDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	// TODO: Implement database query
	return nil, fmt.Errorf("database data source not implemented yet")
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *DatabaseDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	// TODO: Implement database query for detailed data
	return nil, fmt.Errorf("database data source not implemented yet")
}

// Close closes the database connection
func (ds *DatabaseDataSource) Close() error {
	// TODO: Close database connection
	return nil
}

// APIDataSource implements DataSource for API-based data
type APIDataSource struct {
	baseURL string
	apiKey  string
	// Add HTTP client and other fields as needed
}

// NewAPIDataSource creates a new API data source
func NewAPIDataSource() *APIDataSource {
	return &APIDataSource{}
}

// Initialize initializes the API data source
func (ds *APIDataSource) Initialize(config map[string]interface{}) error {
	baseURL, ok := config["base_url"].(string)
	if !ok || baseURL == "" {
		return fmt.Errorf("base_url is required for API data source")
	}
	ds.baseURL = baseURL

	if apiKey, ok := config["api_key"].(string); ok {
		ds.apiKey = apiKey
	}

	// TODO: Initialize HTTP client
	return fmt.Errorf("API data source not implemented yet")
}

// GetEntries retrieves data entries from the API
func (ds *APIDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
	// TODO: Implement API call
	return nil, fmt.Errorf("API data source not implemented yet")
}

// GetDetailedData retrieves detailed data for a specific entry
func (ds *APIDataSource) GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error) {
	// TODO: Implement API call for detailed data
	return nil, fmt.Errorf("API data source not implemented yet")
}

// Close closes the API data source
func (ds *APIDataSource) Close() error {
	// TODO: Close HTTP client
	return nil
}
