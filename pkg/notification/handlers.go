package notification

import (
	"context"
	"fmt"
	"log"

	"general-ai-agent-structure/pkg/agent"
)

// ConsoleNotificationHandler implements NotificationHandler for console output
type ConsoleNotificationHandler struct {
	enabled     bool
	colorOutput bool
	logLevel    string
}

// NewConsoleNotificationHandler creates a new console notification handler
func NewConsoleNotificationHandler() *ConsoleNotificationHandler {
	return &ConsoleNotificationHandler{
		enabled:     true,
		colorOutput: true,
		logLevel:    "info",
	}
}

// Initialize initializes the console notification handler
func (h *ConsoleNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.enabled = enabled
	}
	
	if colorOutput, ok := config["color_output"].(bool); ok {
		h.colorOutput = colorOutput
	}
	
	if logLevel, ok := config["log_level"].(string); ok {
		h.logLevel = logLevel
	}
	
	return nil
}

// SendNotification sends a notification to console
func (h *ConsoleNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	if !h.enabled {
		return nil
	}
	
	// Check if we should send notification based on severity
	if !h.shouldNotify(report.Analysis.Severity) {
		return nil
	}
	
	h.printNotification(report)
	return nil
}

// shouldNotify determines if notification should be sent based on severity
func (h *ConsoleNotificationHandler) shouldNotify(severity string) bool {
	switch h.logLevel {
	case "critical":
		return severity == "critical"
	case "high":
		return severity == "critical" || severity == "high"
	case "medium":
		return severity == "critical" || severity == "high" || severity == "medium"
	case "low", "info":
		return true
	default:
		return true
	}
}

// printNotification prints a formatted notification to console
func (h *ConsoleNotificationHandler) printNotification(report agent.Report) {
	severityIcon := h.getSeverityIcon(report.Analysis.Severity)
	categoryIcon := h.getCategoryIcon(report.Analysis.Category)
	
	fmt.Printf("\n%s ALERT: %s (Severity: %s)\n", severityIcon, report.Analysis.ErrorType, report.Analysis.Severity)
	fmt.Printf("%s Category: %s | Retryable: %t\n", categoryIcon, report.Analysis.Category, report.Analysis.IsRetryable)
	fmt.Printf("💼 Business Impact: %s\n", report.Analysis.BusinessImpact)
	fmt.Printf("🎯 Suggested Action: %s\n", report.Analysis.SuggestedAction)
	fmt.Printf("📋 Entry ID: %s | Title: %s\n", report.DataEntry.ID, report.DataEntry.Title)
	fmt.Printf("⏰ Timestamp: %s\n\n", report.Timestamp)
}

// getSeverityIcon returns an icon based on severity
func (h *ConsoleNotificationHandler) getSeverityIcon(severity string) string {
	if !h.colorOutput {
		return "[" + severity + "]"
	}
	
	switch severity {
	case "critical":
		return "🚨"
	case "high":
		return "⚠️"
	case "medium":
		return "⚡"
	case "low":
		return "ℹ️"
	default:
		return "📋"
	}
}

// getCategoryIcon returns an icon based on category
func (h *ConsoleNotificationHandler) getCategoryIcon(category string) string {
	if !h.colorOutput {
		return "[" + category + "]"
	}
	
	switch category {
	case "infrastructure":
		return "🏗️"
	case "application":
		return "💻"
	case "security":
		return "🔒"
	case "performance":
		return "⚡"
	case "acquiring":
		return "💳"
	case "issuing":
		return "🏦"
	case "compliance":
		return "📋"
	default:
		return "📊"
	}
}

// Close closes the console notification handler
func (h *ConsoleNotificationHandler) Close() error {
	return nil
}

// WebhookNotificationHandler implements NotificationHandler for webhook notifications
type WebhookNotificationHandler struct {
	enabled bool
	url     string
	// TODO: Add HTTP client and authentication
}

// NewWebhookNotificationHandler creates a new webhook notification handler
func NewWebhookNotificationHandler() *WebhookNotificationHandler {
	return &WebhookNotificationHandler{}
}

// Initialize initializes the webhook notification handler
func (h *WebhookNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.enabled = enabled
	}
	
	if url, ok := config["url"].(string); ok {
		h.url = url
	}
	
	if !h.enabled || h.url == "" {
		h.enabled = false
		return nil
	}
	
	// TODO: Initialize HTTP client
	return fmt.Errorf("webhook notification handler not implemented yet")
}

// SendNotification sends a notification via webhook
func (h *WebhookNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	if !h.enabled {
		return nil
	}
	
	// TODO: Implement webhook call
	return fmt.Errorf("webhook notification handler not implemented yet")
}

// Close closes the webhook notification handler
func (h *WebhookNotificationHandler) Close() error {
	return nil
}

// SlackNotificationHandler implements NotificationHandler for Slack notifications
type SlackNotificationHandler struct {
	enabled    bool
	webhookURL string
	channel    string
	username   string
	// TODO: Add Slack client
}

// NewSlackNotificationHandler creates a new Slack notification handler
func NewSlackNotificationHandler() *SlackNotificationHandler {
	return &SlackNotificationHandler{}
}

// Initialize initializes the Slack notification handler
func (h *SlackNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.enabled = enabled
	}
	
	if webhookURL, ok := config["webhook_url"].(string); ok {
		h.webhookURL = webhookURL
	}
	
	if channel, ok := config["channel"].(string); ok {
		h.channel = channel
	}
	
	if username, ok := config["username"].(string); ok {
		h.username = username
	} else {
		h.username = "AI Agent"
	}
	
	if !h.enabled || h.webhookURL == "" {
		h.enabled = false
		return nil
	}
	
	// TODO: Initialize Slack client
	return fmt.Errorf("Slack notification handler not implemented yet")
}

// SendNotification sends a notification to Slack
func (h *SlackNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	if !h.enabled {
		return nil
	}
	
	// TODO: Implement Slack message
	return fmt.Errorf("Slack notification handler not implemented yet")
}

// Close closes the Slack notification handler
func (h *SlackNotificationHandler) Close() error {
	return nil
}

// EmailNotificationHandler implements NotificationHandler for email notifications
type EmailNotificationHandler struct {
	enabled    bool
	smtpServer string
	smtpPort   int
	username   string
	password   string
	fromEmail  string
	toEmails   []string
	// TODO: Add SMTP client
}

// NewEmailNotificationHandler creates a new email notification handler
func NewEmailNotificationHandler() *EmailNotificationHandler {
	return &EmailNotificationHandler{}
}

// Initialize initializes the email notification handler
func (h *EmailNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.enabled = enabled
	}
	
	if smtpServer, ok := config["smtp_server"].(string); ok {
		h.smtpServer = smtpServer
	}
	
	if smtpPort, ok := config["smtp_port"].(int); ok {
		h.smtpPort = smtpPort
	} else {
		h.smtpPort = 587 // default SMTP port
	}
	
	if username, ok := config["username"].(string); ok {
		h.username = username
	}
	
	if password, ok := config["password"].(string); ok {
		h.password = password
	}
	
	if fromEmail, ok := config["from_email"].(string); ok {
		h.fromEmail = fromEmail
	}
	
	if toEmails, ok := config["to_emails"].([]interface{}); ok {
		h.toEmails = make([]string, len(toEmails))
		for i, email := range toEmails {
			if str, ok := email.(string); ok {
				h.toEmails[i] = str
			}
		}
	} else if toEmails, ok := config["to_emails"].([]string); ok {
		h.toEmails = toEmails
	}
	
	if !h.enabled || h.smtpServer == "" || len(h.toEmails) == 0 {
		h.enabled = false
		return nil
	}
	
	// TODO: Initialize SMTP client
	return fmt.Errorf("email notification handler not implemented yet")
}

// SendNotification sends a notification via email
func (h *EmailNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	if !h.enabled {
		return nil
	}
	
	// TODO: Implement email sending
	return fmt.Errorf("email notification handler not implemented yet")
}

// Close closes the email notification handler
func (h *EmailNotificationHandler) Close() error {
	return nil
}

// CompositeNotificationHandler combines multiple notification handlers
type CompositeNotificationHandler struct {
	handlers []agent.NotificationHandler
}

// NewCompositeNotificationHandler creates a new composite notification handler
func NewCompositeNotificationHandler() *CompositeNotificationHandler {
	return &CompositeNotificationHandler{
		handlers: make([]agent.NotificationHandler, 0),
	}
}

// AddHandler adds a notification handler to the composite
func (h *CompositeNotificationHandler) AddHandler(handler agent.NotificationHandler) {
	h.handlers = append(h.handlers, handler)
}

// Initialize initializes the composite notification handler
func (h *CompositeNotificationHandler) Initialize(config map[string]interface{}) error {
	// Initialize individual handlers if configs are provided
	if handlersConfig, ok := config["handlers"].([]interface{}); ok {
		for i, handlerConfig := range handlersConfig {
			if i < len(h.handlers) {
				if configMap, ok := handlerConfig.(map[string]interface{}); ok {
					if err := h.handlers[i].Initialize(configMap); err != nil {
						log.Printf("Failed to initialize notification handler %d: %v", i, err)
					}
				}
			}
		}
	}
	
	return nil
}

// SendNotification sends notifications through all handlers
func (h *CompositeNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	var lastError error
	
	for i, handler := range h.handlers {
		if err := handler.SendNotification(ctx, report); err != nil {
			log.Printf("Notification handler %d failed: %v", i, err)
			lastError = err
		}
	}
	
	return lastError
}

// Close closes all notification handlers
func (h *CompositeNotificationHandler) Close() error {
	var lastError error
	
	for i, handler := range h.handlers {
		if err := handler.Close(); err != nil {
			log.Printf("Failed to close notification handler %d: %v", i, err)
			lastError = err
		}
	}
	
	return lastError
}
