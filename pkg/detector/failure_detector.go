package detector

import (
	"fmt"
	"strings"

	"general-ai-agent-structure/pkg/agent"
)

// KeywordFailureDetector implements FailureDetector using keyword matching
type KeywordFailureDetector struct {
	enabled      bool
	keywords     []string
	logTypes     []string
	caseSensitive bool
}

// NewKeywordFailureDetector creates a new keyword-based failure detector
func NewKeywordFailureDetector() *KeywordFailureDetector {
	return &KeywordFailureDetector{
		enabled:      true,
		caseSensitive: false,
	}
}

// Configure configures the failure detector
func (fd *KeywordFailureDetector) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		fd.enabled = enabled
	}
	
	if keywords, ok := config["keywords"].([]interface{}); ok {
		fd.keywords = make([]string, len(keywords))
		for i, keyword := range keywords {
			if str, ok := keyword.(string); ok {
				fd.keywords[i] = str
			}
		}
	} else if keywords, ok := config["keywords"].([]string); ok {
		fd.keywords = keywords
	}
	
	if logTypes, ok := config["log_types"].([]interface{}); ok {
		fd.logTypes = make([]string, len(logTypes))
		for i, logType := range logTypes {
			if str, ok := logType.(string); ok {
				fd.logTypes[i] = str
			}
		}
	} else if logTypes, ok := config["log_types"].([]string); ok {
		fd.logTypes = logTypes
	}
	
	if caseSensitive, ok := config["case_sensitive"].(bool); ok {
		fd.caseSensitive = caseSensitive
	}
	
	// Set default keywords if none provided
	if len(fd.keywords) == 0 {
		fd.keywords = []string{
			"error", "failed", "failure", "exception", "timeout", 
			"rejected", "declined", "invalid", "critical", "fatal",
		}
	}
	
	// Set default log types if none provided
	if len(fd.logTypes) == 0 {
		fd.logTypes = []string{"error", "critical", "fatal"}
	}
	
	return nil
}

// IsFailure determines if a data entry represents a failure
func (fd *KeywordFailureDetector) IsFailure(entry agent.DataEntry) bool {
	if !fd.enabled {
		return false
	}
	
	// Check log type
	for _, logType := range fd.logTypes {
		if fd.matchString(entry.Type, logType) {
			return true
		}
	}
	
	// Check for keywords in title and message
	content := entry.Title + " " + entry.Message
	if !fd.caseSensitive {
		content = strings.ToLower(content)
	}
	
	for _, keyword := range fd.keywords {
		searchKeyword := keyword
		if !fd.caseSensitive {
			searchKeyword = strings.ToLower(keyword)
		}
		
		if strings.Contains(content, searchKeyword) {
			return true
		}
	}
	
	return false
}

// GetFailureReason returns the reason why an entry is considered a failure
func (fd *KeywordFailureDetector) GetFailureReason(entry agent.DataEntry) string {
	if !fd.enabled {
		return "failure detection disabled"
	}
	
	// Check log type
	for _, logType := range fd.logTypes {
		if fd.matchString(entry.Type, logType) {
			return fmt.Sprintf("log type matches failure type: %s", logType)
		}
	}
	
	// Check for keywords
	content := entry.Title + " " + entry.Message
	if !fd.caseSensitive {
		content = strings.ToLower(content)
	}
	
	for _, keyword := range fd.keywords {
		searchKeyword := keyword
		if !fd.caseSensitive {
			searchKeyword = strings.ToLower(keyword)
		}
		
		if strings.Contains(content, searchKeyword) {
			return fmt.Sprintf("content contains failure keyword: %s", keyword)
		}
	}
	
	return "no failure detected"
}

// matchString performs string matching based on case sensitivity setting
func (fd *KeywordFailureDetector) matchString(text, pattern string) bool {
	if fd.caseSensitive {
		return text == pattern
	}
	return strings.ToLower(text) == strings.ToLower(pattern)
}

// RegexFailureDetector implements FailureDetector using regular expressions
type RegexFailureDetector struct {
	enabled  bool
	patterns []string
	// TODO: Add compiled regex patterns
}

// NewRegexFailureDetector creates a new regex-based failure detector
func NewRegexFailureDetector() *RegexFailureDetector {
	return &RegexFailureDetector{
		enabled: true,
	}
}

// Configure configures the regex failure detector
func (fd *RegexFailureDetector) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		fd.enabled = enabled
	}
	
	if patterns, ok := config["patterns"].([]interface{}); ok {
		fd.patterns = make([]string, len(patterns))
		for i, pattern := range patterns {
			if str, ok := pattern.(string); ok {
				fd.patterns[i] = str
			}
		}
	} else if patterns, ok := config["patterns"].([]string); ok {
		fd.patterns = patterns
	}
	
	// TODO: Compile regex patterns
	return fmt.Errorf("regex failure detector not implemented yet")
}

// IsFailure determines if a data entry represents a failure using regex
func (fd *RegexFailureDetector) IsFailure(entry agent.DataEntry) bool {
	// TODO: Implement regex matching
	return false
}

// GetFailureReason returns the reason why an entry is considered a failure
func (fd *RegexFailureDetector) GetFailureReason(entry agent.DataEntry) string {
	// TODO: Implement regex matching reason
	return "regex failure detector not implemented"
}

// MLFailureDetector implements FailureDetector using machine learning
type MLFailureDetector struct {
	enabled   bool
	modelPath string
	threshold float64
	// TODO: Add ML model fields
}

// NewMLFailureDetector creates a new ML-based failure detector
func NewMLFailureDetector() *MLFailureDetector {
	return &MLFailureDetector{
		enabled:   true,
		threshold: 0.5,
	}
}

// Configure configures the ML failure detector
func (fd *MLFailureDetector) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		fd.enabled = enabled
	}
	
	if modelPath, ok := config["model_path"].(string); ok {
		fd.modelPath = modelPath
	}
	
	if threshold, ok := config["threshold"].(float64); ok {
		fd.threshold = threshold
	}
	
	// TODO: Load ML model
	return fmt.Errorf("ML failure detector not implemented yet")
}

// IsFailure determines if a data entry represents a failure using ML
func (fd *MLFailureDetector) IsFailure(entry agent.DataEntry) bool {
	// TODO: Implement ML prediction
	return false
}

// GetFailureReason returns the reason why an entry is considered a failure
func (fd *MLFailureDetector) GetFailureReason(entry agent.DataEntry) string {
	// TODO: Implement ML prediction reason
	return "ML failure detector not implemented"
}

// CompositeFailureDetector combines multiple failure detectors
type CompositeFailureDetector struct {
	detectors []agent.FailureDetector
	strategy  string // "any", "all", "majority"
}

// NewCompositeFailureDetector creates a new composite failure detector
func NewCompositeFailureDetector(strategy string) *CompositeFailureDetector {
	return &CompositeFailureDetector{
		detectors: make([]agent.FailureDetector, 0),
		strategy:  strategy,
	}
}

// AddDetector adds a failure detector to the composite
func (fd *CompositeFailureDetector) AddDetector(detector agent.FailureDetector) {
	fd.detectors = append(fd.detectors, detector)
}

// Configure configures the composite failure detector
func (fd *CompositeFailureDetector) Configure(config map[string]interface{}) error {
	if strategy, ok := config["strategy"].(string); ok {
		fd.strategy = strategy
	}
	
	// Configure individual detectors if configs are provided
	if detectorsConfig, ok := config["detectors"].([]interface{}); ok {
		for i, detectorConfig := range detectorsConfig {
			if i < len(fd.detectors) {
				if configMap, ok := detectorConfig.(map[string]interface{}); ok {
					fd.detectors[i].Configure(configMap)
				}
			}
		}
	}
	
	return nil
}

// IsFailure determines if a data entry represents a failure using composite strategy
func (fd *CompositeFailureDetector) IsFailure(entry agent.DataEntry) bool {
	if len(fd.detectors) == 0 {
		return false
	}
	
	positiveCount := 0
	for _, detector := range fd.detectors {
		if detector.IsFailure(entry) {
			positiveCount++
		}
	}
	
	switch fd.strategy {
	case "any":
		return positiveCount > 0
	case "all":
		return positiveCount == len(fd.detectors)
	case "majority":
		return positiveCount > len(fd.detectors)/2
	default:
		return positiveCount > 0 // default to "any"
	}
}

// GetFailureReason returns the reason why an entry is considered a failure
func (fd *CompositeFailureDetector) GetFailureReason(entry agent.DataEntry) string {
	reasons := make([]string, 0)
	
	for i, detector := range fd.detectors {
		if detector.IsFailure(entry) {
			reason := detector.GetFailureReason(entry)
			reasons = append(reasons, fmt.Sprintf("detector_%d: %s", i, reason))
		}
	}
	
	if len(reasons) == 0 {
		return "no failure detected by any detector"
	}
	
	return fmt.Sprintf("composite detection (%s): %s", fd.strategy, strings.Join(reasons, "; "))
}
