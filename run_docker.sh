#!/bin/bash

# AI Failatun Agent Docker Runner (Local LLM)

echo "🚀 Starting AI Order Failure Agent with Docker and Local LLM..."

# Check if local LLM is running
echo "🔍 Checking if local LLM is running on localhost:1234..."
if ! curl -s http://localhost:1234/v1/models > /dev/null; then
    echo "❌ Local LLM is not running on localhost:1234!"
    echo "Please start your local LLM server first."
    echo "Example: lm-studio, ollama, or other local LLM server"
    exit 1
fi

echo "✅ Local LLM detected on localhost:1234"

# Create logs directory if it doesn't exist
mkdir -p logs

# Build and run the container
echo "🔨 Building Docker image..."
docker-compose build

echo "🚀 Starting AI agent..."
docker-compose up -d

echo "✅ AI agent is running with local LLM!"
echo "📊 View logs with: docker-compose logs -f"
echo "🛑 Stop with: docker-compose down"
