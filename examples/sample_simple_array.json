[{"id": "log_001", "type": "error", "title": "Payment Processing Error", "message": "Credit card authorization failed", "entity": "payment-service", "location": "payment.authorize()"}, {"id": "log_002", "type": "warning", "title": "Slow Query Detected", "message": "Database query took longer than 5 seconds", "entity": "database-service", "location": "query.execute()"}, {"id": "log_003", "type": "error", "title": "File Upload Failed", "message": "Unable to upload file to storage", "entity": "file-service", "location": "file.upload()"}]