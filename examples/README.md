# Example Configurations

This directory contains example configurations for different domains and use cases.

## Available Examples

### 1. Fintech Configuration (`fintech_config.json`)
Specialized configuration for financial services and payment systems:

- **Domain**: Fintech/Payment Systems
- **Categories**: acquiring, issuing, infrastructure, compliance, risk
- **Keywords**: error, failed, failure, timeout, rejected, declined, invalid
- **Use Cases**: 
  - Payment processing failures
  - Card authorization issues
  - Compliance violations
  - Settlement problems
  - Fraud detection alerts

**Usage:**
```bash
go run main.go examples/fintech_config.json
```

### 2. E-commerce Configuration (`ecommerce_config.json`)
Optimized for online retail and e-commerce platforms:

- **Domain**: E-commerce
- **Categories**: order_management, customer_experience, payment, infrastructure
- **Keywords**: error, failed, failure, timeout, cart, checkout, payment
- **Use Cases**:
  - Shopping cart issues
  - Checkout failures
  - Order processing problems
  - Inventory management errors
  - Customer experience issues

**Usage:**
```bash
go run main.go examples/ecommerce_config.json
```

## Creating Your Own Configuration

### Method 1: Use Configuration Generator
```bash
# List available templates
go run cmd/config-generator/main.go list

# Generate configuration from template
go run cmd/config-generator/main.go generate fintech my_config.json
```

### Method 2: Copy and Modify
```bash
# Copy an existing example
cp examples/fintech_config.json my_custom_config.json

# Edit the configuration
vim my_custom_config.json

# Run with your custom config
go run main.go my_custom_config.json
```

## Configuration Sections

### Agent Information
```json
{
  "agent": {
    "name": "my-agent-name",
    "version": "1.0.0",
    "type": "log_analyzer",
    "description": "Description of what this agent does"
  }
}
```

### Data Sources
```json
{
  "data_sources": {
    "primary": {
      "type": "json_file",           // json_file, database, api
      "path": "logs.json",           // Path to data file
      "format": "log_index",         // log_index, simple_array, custom
      "detailed_logs_path": "logs/"  // Path to detailed log files
    }
  }
}
```

### AI Provider
```json
{
  "ai_provider": {
    "type": "openai_compatible",                              // openai_compatible, anthropic
    "api_url": "http://localhost:1234/v1/chat/completions",   // API endpoint
    "model_name": "qwen/qwen3-4b-thinking-2507",             // Model name
    "temperature": 0.1,                                       // Creativity (0.0-1.0)
    "max_tokens": 2000,                                       // Max response length
    "timeout_seconds": 60                                     // Request timeout
  }
}
```

### Processing Configuration
```json
{
  "processing": {
    "check_interval": 30,           // Seconds between data checks
    "max_retries": 3,               // Max retry attempts
    "batch_size": 10,               // Entries per batch
    "parallel_processing": false,   // Enable parallel processing
    "failure_detection": {
      "enabled": true,
      "keywords": ["error", "failed", "timeout"],
      "log_types": ["error", "critical"]
    }
  }
}
```

### Analysis Configuration
```json
{
  "analysis": {
    "domain": "fintech",                    // Domain for specialized prompts
    "prompt_template": "fintech",           // Template to use
    "severity_threshold": "medium",         // Minimum severity to process
    "categories": ["acquiring", "issuing"], // Expected categories
    "output_format": "json"                 // Output format
  }
}
```

### Notifications
```json
{
  "notifications": {
    "enabled": true,
    "channels": {
      "console": {"enabled": true},
      "webhook": {"enabled": false, "url": "https://..."},
      "slack": {"enabled": false, "webhook_url": "https://..."}
    }
  }
}
```

### Output Configuration
```json
{
  "output": {
    "reports_directory": "reports/",                    // Output directory
    "filename_pattern": "analysis_{id}_{timestamp}.json", // File naming pattern
    "include_raw_data": false,                          // Include original data
    "pretty_print": true                                // Format JSON nicely
  }
}
```

## Tips for Customization

1. **Domain Selection**: Choose the domain that best matches your use case (fintech, ecommerce, general)
2. **Keywords**: Customize failure detection keywords for your specific terminology
3. **Categories**: Adjust categories to match your system's error classification
4. **AI Model**: Use a model appropriate for your language and complexity needs
5. **Check Interval**: Set based on your data volume and urgency requirements
6. **Notifications**: Enable channels that fit your alerting workflow

## Testing Your Configuration

1. **Validate Syntax**: Ensure your JSON is valid
2. **Test AI Connection**: Verify your AI provider is accessible
3. **Check Data Format**: Ensure your data matches the expected format
4. **Run Dry Test**: Test with a small dataset first
5. **Monitor Output**: Check generated reports and notifications
