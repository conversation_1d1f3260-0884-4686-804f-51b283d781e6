{"agent": {"name": "webhook-realtime-analyzer", "version": "1.0.0", "type": "realtime_analyzer", "description": "Real-time AI agent for webhook-based data analysis"}, "data_sources": {"primary": {"type": "webhook", "enabled": true, "port": 8080, "endpoint": "/webhook"}}, "ai_provider": {"type": "openai_compatible", "api_url": "http://localhost:1234/v1/chat/completions", "model_name": "qwen/qwen3-4b-thinking-2507", "temperature": 0.1, "max_tokens": 2000, "timeout_seconds": 60}, "processing": {"check_interval": 5, "max_retries": 3, "batch_size": 20, "parallel_processing": false, "failure_detection": {"enabled": true, "keywords": ["error", "failed", "failure", "exception", "timeout", "critical", "alert"], "log_types": ["error", "critical", "alert"]}}, "analysis": {"domain": "general", "prompt_template": "general", "severity_threshold": "medium", "categories": ["infrastructure", "application", "security", "performance", "webhook"], "output_format": "json"}, "notifications": {"enabled": true, "channels": {"console": {"enabled": true}, "webhook": {"enabled": false, "url": ""}, "slack": {"enabled": false, "webhook_url": ""}}}, "output": {"reports_directory": "reports/", "filename_pattern": "webhook_analysis_{id}_{timestamp}.json", "include_raw_data": true, "pretty_print": true}}