# Webhook Data Source Kullanımı

Webhook data source, real-time veri akışı için HTTP webhook endpoint'i sağlar. Gelen POST isteklerini otomatik olarak DataEntry formatına dönüştürür ve AI analizine gönderir.

## 🚀 Hızlı Başlangıç

### 1. Webhook Agent Oluştur
```bash
# Webhook template'ini kullan
go run cmd/config-generator/main.go generate webhook

# Agent'ı başlat
go run main.go ai_config.json
```

### 2. Webhook Endpoint'i Test Et
```bash
# Health check
curl http://localhost:8080/health

# Test webhook'u gönder
curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "type": "error",
    "title": "Database Connection Failed",
    "message": "Unable to connect to primary database",
    "entity": "user-service",
    "location": "database.connect()"
  }'
```

## 📋 Konfigürasyon

### Webhook Data Source Ayarları
```json
{
  "data_sources": {
    "primary": {
      "type": "webhook",
      "enabled": true,
      "port": 8080,
      "endpoint": "/webhook"
    }
  }
}
```

### Konfigürasyon Parametreleri
- **enabled**: Webhook server'ı aktif/pasif (default: true)
- **port**: Dinlenecek port (default: 8080)
- **endpoint**: Webhook endpoint yolu (default: "/webhook")

## 📨 Webhook Veri Formatları

### 1. Standart Format (Önerilen)
```json
{
  "type": "error",
  "title": "Payment Processing Error",
  "message": "Credit card authorization failed",
  "entity": "payment-service",
  "location": "payment.authorize()",
  "proto": "HTTPS"
}
```

### 2. Minimal Format
```json
{
  "message": "System error occurred",
  "type": "error"
}
```

### 3. Custom Format
```json
{
  "event": "user_login_failed",
  "description": "Invalid credentials provided",
  "severity": "medium",
  "source": "auth-service",
  "custom_field": "custom_value"
}
```

### 4. Nested Data Format
```json
{
  "alert": {
    "name": "High CPU Usage",
    "description": "CPU usage exceeded 90%",
    "severity": "critical"
  },
  "metadata": {
    "server": "web-01",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 🔄 Veri Dönüşümü

Webhook data source gelen veriyi otomatik olarak DataEntry formatına dönüştürür:

### Gelen Webhook:
```json
{
  "type": "error",
  "title": "API Timeout",
  "message": "External API call timed out after 30 seconds",
  "service": "order-service"
}
```

### Dönüştürülen DataEntry:
```json
{
  "id": "webhook_1640995200123456789",
  "type": "error",
  "title": "API Timeout",
  "message": "External API call timed out after 30 seconds",
  "entity": "webhook",
  "location": "",
  "proto": "HTTP",
  "data": {
    "type": "error",
    "title": "API Timeout",
    "message": "External API call timed out after 30 seconds",
    "service": "order-service"
  },
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "source": "webhook",
    "content_type": "application/json",
    "user_agent": "curl/7.68.0",
    "remote_addr": "*************"
  }
}
```

## 🎯 Kullanım Senaryoları

### 1. Monitoring Sistemi Entegrasyonu
```bash
# Prometheus AlertManager webhook
curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "alerts": [
      {
        "status": "firing",
        "labels": {
          "alertname": "HighMemoryUsage",
          "instance": "server-01",
          "severity": "critical"
        },
        "annotations": {
          "description": "Memory usage is above 95%"
        }
      }
    ]
  }'
```

### 2. Application Error Reporting
```bash
# Application error webhook
curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "error": {
      "type": "NullPointerException",
      "message": "Null pointer exception in user service",
      "stack_trace": "java.lang.NullPointerException...",
      "request_id": "req_123456"
    },
    "context": {
      "user_id": "user_789",
      "endpoint": "/api/users/profile",
      "method": "GET"
    }
  }'
```

### 3. Security Event Reporting
```bash
# Security event webhook
curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "security_event": {
      "type": "failed_login_attempt",
      "user": "admin",
      "ip_address": "*************",
      "attempts": 5,
      "blocked": true
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }'
```

## 📊 Webhook Response

Başarılı webhook isteği yanıtı:
```json
{
  "status": "success",
  "message": "Webhook received",
  "entry_id": "webhook_1640995200123456789"
}
```

Hata yanıtı:
```json
{
  "status": "error",
  "message": "Invalid JSON format"
}
```

## 🔧 Gelişmiş Konfigürasyon

### Real-time Processing için Optimize Edilmiş Ayarlar
```json
{
  "processing": {
    "check_interval": 1,        // 1 saniyede bir kontrol
    "max_retries": 2,           // Hızlı retry
    "batch_size": 50,           // Büyük batch size
    "parallel_processing": true  // Paralel işlem
  }
}
```

### Webhook-Specific Failure Detection
```json
{
  "failure_detection": {
    "enabled": true,
    "keywords": [
      "error", "failed", "exception", "timeout", 
      "critical", "alert", "warning", "panic"
    ],
    "log_types": ["error", "critical", "alert", "warning"]
  }
}
```

## 🛡️ Güvenlik Considerations

### 1. IP Whitelist (Gelecek özellik)
```json
{
  "data_sources": {
    "primary": {
      "type": "webhook",
      "security": {
        "allowed_ips": ["***********/24", "10.0.0.0/8"],
        "require_auth": true,
        "api_key": "your-secret-key"
      }
    }
  }
}
```

### 2. Rate Limiting (Gelecek özellik)
```json
{
  "data_sources": {
    "primary": {
      "type": "webhook",
      "rate_limiting": {
        "requests_per_minute": 100,
        "burst_size": 20
      }
    }
  }
}
```

## 🔍 Monitoring & Debugging

### Health Check Endpoint
```bash
curl http://localhost:8080/health
```

Yanıt:
```json
{
  "status": "healthy",
  "queue_size": 5,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Log Monitoring
Agent loglarında webhook aktivitesini takip edebilirsiniz:
```
🌐 Webhook server starting on port 8080, endpoint /webhook
📨 Webhook data received: webhook_1640995200123456789
⚠️ Webhook queue full, dropping entry: webhook_1640995200123456790
🛑 Shutting down webhook server...
```

## 💡 Best Practices

1. **Queue Size**: Yoğun trafik için queue buffer'ını artırın
2. **Check Interval**: Real-time için düşük interval kullanın (1-5 saniye)
3. **Batch Size**: Performans için uygun batch size seçin
4. **Error Handling**: Webhook gönderen sistemde retry mekanizması kullanın
5. **Monitoring**: Health endpoint'ini düzenli kontrol edin

## 🚨 Troubleshooting

### Problem: Webhook'lar alınmıyor
- Port'un açık olduğunu kontrol edin
- Firewall ayarlarını kontrol edin
- Health endpoint'ini test edin

### Problem: Queue doluyor
- Check interval'ı azaltın
- Batch size'ı artırın
- Parallel processing'i aktif edin

### Problem: JSON parse hatası
- Gönderilen JSON formatını kontrol edin
- Content-Type header'ını kontrol edin
- Webhook payload'ını validate edin
