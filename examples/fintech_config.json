{"agent": {"name": "fintech-payment-analyzer", "version": "1.0.0", "type": "log_analyzer", "description": "AI agent for fintech payment system analysis"}, "data_sources": {"primary": {"type": "json_file", "path": "logs.json", "format": "log_index", "detailed_logs_path": "logs/"}}, "ai_provider": {"type": "openai_compatible", "api_url": "http://localhost:1234/v1/chat/completions", "model_name": "qwen/qwen3-4b-thinking-2507", "temperature": 0.1, "max_tokens": 2000, "timeout_seconds": 60}, "processing": {"check_interval": 30, "max_retries": 3, "batch_size": 10, "parallel_processing": false, "failure_detection": {"enabled": true, "keywords": ["error", "failed", "failure", "timeout", "rejected", "declined", "invalid"], "log_types": ["error", "critical"]}}, "analysis": {"domain": "fintech", "prompt_template": "fintech", "severity_threshold": "medium", "categories": ["acquiring", "issuing", "infrastructure", "compliance", "risk"], "output_format": "json"}, "notifications": {"enabled": true, "channels": {"console": {"enabled": true}, "webhook": {"enabled": false}, "slack": {"enabled": false}}}, "output": {"reports_directory": "reports/", "filename_pattern": "analysis_{id}_{timestamp}.json", "include_raw_data": false, "pretty_print": true}}