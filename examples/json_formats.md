# JSON File Data Source - Desteklenen Formatlar

JSON File Data Source artık çok daha esnek ve genel amaçlı. Farklı JSON formatlarını otomatik olarak algılar ve işler.

## 🎯 Desteklenen JSON Formatları

### 1. **Auto-Detect Format** (Önerilen)
```json
{
  "data_sources": {
    "primary": {
      "type": "json_file",
      "path": "data.json",
      "format": "auto_detect"
    }
  }
}
```

### 2. **Index Format** (Metadata ile)
```json
{
  "page": 1,
  "total": 100,
  "items": [
    {
      "id": "item_001",
      "type": "error",
      "title": "Database Error",
      "message": "Connection failed"
    }
  ]
}
```

**Desteklenen array field'ları:**
- `items` (önerilen)
- `data`
- `records`
- `entries`
- `events`
- `rows` (legacy support)

### 3. **Simple Array Format**
```json
[
  {
    "id": "event_001",
    "type": "warning",
    "message": "High CPU usage detected"
  },
  {
    "id": "event_002", 
    "type": "error",
    "message": "Service unavailable"
  }
]
```

### 4. **Custom Format**
```json
{
  "alerts": [
    {
      "alert_name": "Memory Usage",
      "description": "Memory usage is high",
      "severity": "critical"
    }
  ],
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### 5. **Single Object Format**
```json
{
  "event_id": "single_001",
  "event_type": "error",
  "description": "Single event occurred",
  "source": "application"
}
```

## 🔧 Field Mapping Sistemi

### Default Field Mappings
```json
{
  "data_sources": {
    "primary": {
      "type": "json_file",
      "path": "data.json",
      "field_mappings": {
        "id": "id",
        "type": "type", 
        "title": "title",
        "message": "message",
        "entity": "entity",
        "location": "location",
        "proto": "proto"
      }
    }
  }
}
```

### Custom Field Mappings
```json
{
  "data_sources": {
    "primary": {
      "type": "json_file",
      "path": "alerts.json",
      "field_mappings": {
        "id": "alert_id",
        "type": "severity",
        "title": "alert_name", 
        "message": "description",
        "entity": "source_service",
        "location": "component"
      }
    }
  }
}
```

## 📁 Detailed Data Support

### Konfigürasyon
```json
{
  "data_sources": {
    "primary": {
      "type": "json_file",
      "path": "index.json",
      "detailed_data_path": "details/"
    }
  }
}
```

### Desteklenen File Naming Patterns
```
details/
├── 1.json          # 1-indexed (legacy)
├── 0.json          # 0-indexed
├── item_001.json   # ID-based
└── event_002.json  # ID with extension
```

## 🎨 Örnek Kullanım Senaryoları

### 1. **Monitoring Alerts**
```json
{
  "alerts": [
    {
      "alert_id": "alert_001",
      "severity": "critical",
      "alert_name": "High CPU Usage",
      "description": "CPU usage exceeded 90%",
      "source_service": "web-server",
      "component": "cpu_monitor"
    }
  ]
}
```

**Konfigürasyon:**
```json
{
  "field_mappings": {
    "id": "alert_id",
    "type": "severity",
    "title": "alert_name",
    "message": "description", 
    "entity": "source_service",
    "location": "component"
  }
}
```

### 2. **Application Logs**
```json
{
  "logs": [
    {
      "log_id": "log_001",
      "level": "error",
      "event": "Database Connection Failed",
      "details": "Unable to connect to primary database",
      "service": "user-service",
      "method": "getUserById"
    }
  ]
}
```

**Konfigürasyon:**
```json
{
  "field_mappings": {
    "id": "log_id",
    "type": "level",
    "title": "event",
    "message": "details",
    "entity": "service", 
    "location": "method"
  }
}
```

### 3. **Security Events**
```json
{
  "security_events": [
    {
      "event_id": "sec_001",
      "threat_level": "high",
      "event_name": "Failed Login Attempt",
      "event_description": "Multiple failed login attempts detected",
      "affected_system": "auth-service",
      "source_ip": "*************"
    }
  ]
}
```

### 4. **IoT Device Data**
```json
{
  "device_events": [
    {
      "device_id": "sensor_001",
      "status": "error",
      "sensor_name": "Temperature Sensor",
      "error_message": "Sensor reading out of range",
      "device_location": "warehouse_a",
      "protocol": "MQTT"
    }
  ]
}
```

## 🔄 Otomatik Dönüşüm

### Gelen Data:
```json
{
  "notifications": [
    {
      "notification_id": "notif_001",
      "priority": "urgent",
      "subject": "System Alert",
      "body": "Critical system error detected"
    }
  ]
}
```

### Dönüştürülen DataEntry:
```json
{
  "id": "notif_001",
  "type": "urgent", 
  "title": "System Alert",
  "message": "Critical system error detected",
  "entity": "",
  "location": "",
  "proto": "",
  "data": {
    "notification_id": "notif_001",
    "priority": "urgent",
    "subject": "System Alert", 
    "body": "Critical system error detected"
  },
  "metadata": {
    "source": "json_file",
    "file_path": "notifications.json",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 🎯 Akıllı Field Detection

Eğer field mapping'de belirtilmemişse, sistem otomatik olarak şu alternatifleri arar:

### Message Field Alternatifleri:
- `description`
- `text`
- `content`
- `body`
- `summary`

### Title Field Alternatifleri:
- `name`
- `subject`
- `event`
- `event_type`
- `alert_name`

### Type Field Alternatifleri:
- `level`
- `severity`
- `priority`
- `status`

## 💡 Best Practices

1. **Auto-detect kullanın**: Çoğu durumda format otomatik algılanır
2. **Field mapping'leri özelleştirin**: Kendi JSON yapınıza uygun mapping'ler tanımlayın
3. **Consistent naming**: JSON field'larınızda tutarlı isimlendirme kullanın
4. **Detailed data**: Büyük veriler için detailed data klasörü kullanın
5. **Test edin**: Farklı JSON formatlarınızı test edin

## 🔍 Debugging

### Log Output:
```
📁 Loading JSON file: data.json
🔍 Auto-detecting format...
✅ Detected format: index_format
📊 Found 25 entries in 'items' field
🔄 Converting entries using field mappings...
✅ Successfully loaded 25 entries
```

### Common Issues:
- **Empty results**: Field mapping'leri kontrol edin
- **Parse errors**: JSON syntax'ını validate edin  
- **Missing detailed data**: File naming pattern'ini kontrol edin
