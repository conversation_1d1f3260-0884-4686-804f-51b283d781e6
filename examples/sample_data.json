{"page": 1, "total": 5, "items": [{"id": "event_001", "type": "error", "title": "Database Connection Failed", "message": "Unable to connect to primary database server", "entity": "user-service", "location": "database.connect()", "timestamp": "2024-01-15T10:30:00Z"}, {"id": "event_002", "type": "warning", "title": "High Memory Usage", "message": "Memory usage exceeded 85% threshold", "entity": "payment-service", "location": "memory.monitor()", "timestamp": "2024-01-15T10:31:00Z"}, {"id": "event_003", "type": "error", "title": "API Timeout", "message": "External API call timed out after 30 seconds", "entity": "notification-service", "location": "api.call()", "timestamp": "2024-01-15T10:32:00Z"}, {"id": "event_004", "type": "critical", "title": "Service Unavailable", "message": "Payment processing service is down", "entity": "payment-gateway", "location": "service.health()", "timestamp": "2024-01-15T10:33:00Z"}, {"id": "event_005", "type": "error", "title": "Authentication Failed", "message": "Invalid API key provided", "entity": "auth-service", "location": "auth.validate()", "timestamp": "2024-01-15T10:34:00Z"}]}