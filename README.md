# AI Order Failure Agent (Go + Local LLM)

An intelligent Go-based agent that monitors payment/order failures and uses a local LLM to analyze and categorize errors with fintech expertise.

## Features

- 🤖 **Local LLM Integration**: Uses your local LLM (LM Studio, Ollama, etc.) instead of cloud APIs
- 📊 **Intelligent Error Analysis**: AI-powered error categorization with fintech domain knowledge
- 🏦 **Fintech Expertise**: Specialized in acquiring/issuing operations, payment systems, and compliance
- 🔔 **Smart Notifications**: Real-time alerts for critical payment failures
- 📈 **Severity Classification**: Automatic priority assignment based on business impact
- ⚡ **High Performance**: Go-based for fast processing and low resource usage
- 🐳 **Docker Support**: Easy deployment with multi-stage Docker builds

## Prerequisites

### Local LLM Server
You need a local LLM server running on `localhost:1234`. Compatible with:

- **LM Studio** (recommended)
- **Ollama** 
- **Text Generation WebUI**
- **vLLM**
- Any OpenAI-compatible local server

#### Setup LM Studio (Recommended)
1. Download and install [LM Studio](https://lmstudio.ai/)
2. Download a model (e.g., <PERSON>wen 3.5 4B)
3. Start the local server on port 1234
4. Test with: `curl http://localhost:1234/v1/models`

#### Setup Ollama Alternative
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Run a model
ollama run qwen2.5:4b

# Serve API on port 1234
ollama serve --host 0.0.0.0:1234
```

## Quick Start

### 1. Run with Go (Recommended)
```bash
# Install Go dependencies
go mod tidy

# Run the agent
go run main.go

# Or build and run binary
go build -o ai-agent main.go
./ai-agent
```

### 2. Run with Docker
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f ai-agent

# Stop
docker-compose down
```

## Configuration

Edit `ai_config.json`:

```json
{
    "log_file": "logs.json",
    "order_file": "order.json",
    "api_url": "http://localhost:1234/v1/chat/completions",
    "model_name": "qwen/qwen3-4b-thinking-2507",
    "temperature": 0.1,
    "max_tokens": 2000,
    "check_interval": 30,
    "notification_enabled": true,
    "webhook_url": "https://your-webhook.com/alerts",
    "slack_webhook": "https://hooks.slack.com/...",
    "max_retries": 3,
    "severity_threshold": "Medium"
}
```

## File Structure

```
├── main.go               # Main Go agent application
├── go.mod               # Go module dependencies
├── go.sum              # Go module checksums
├── ai_config.json      # Configuration file
├── logs.json          # Error logs to monitor
├── logs/              # Detailed log files directory
├── order.json         # Order data
├── reports/           # Generated analysis reports
├── Dockerfile         # Docker configuration
└── docker-compose.yml # Docker Compose setup
```

## How It Works

1. **Monitor**: Scans `logs.json` for failure entries (errors, timeouts, rejections)
2. **Analyze**: Sends error details to local LLM with fintech-specific prompts
3. **Categorize**: Classifies by acquiring/issuing operations, compliance risk, severity
4. **Report**: Generates detailed JSON reports with business impact assessment
5. **Notify**: Logs alerts for critical failures requiring immediate attention

## Fintech Analysis Features

The agent provides specialized analysis for:

### Acquiring Operations
- Merchant onboarding and underwriting issues
- Payment processing failures (auth, capture, settlement)
- Multi-acquirer routing problems
- Chargeback and dispute management
- PCI DSS compliance violations

### Issuing Operations  
- Card provisioning and lifecycle management
- Digital wallet integration issues (Apple/Google Pay)
- Real-time authorization failures
- KYC/AML compliance problems
- Spending controls and limits violations

### Infrastructure Analysis
- API gateway and rate limiting issues
- Database consistency problems
- Event-driven architecture failures
- Fraud detection system alerts
- Regulatory compliance gaps (PSD2, GDPR)

## Sample AI Analysis Output

```json
{
    "error_type": "Vpos Submerchant Payment Processor Information Retrieval Failure",
    "severity": "high",
    "category": "acquiring",
    "description": "System failed to retrieve payment processor configuration for submerchant during Vpos processing",
    "root_cause": "Invalid submerchant configuration or network connectivity issues with payment processor",
    "business_impact": "Failure to process payments for affected submerchants, potential revenue loss",
    "suggested_action": "Verify submerchant configuration, check network connectivity to payment processor",
    "is_retryable": true,
    "compliance_risk": "medium",
    "customer_impact": "Customers unable to complete payments; potential transaction failures"
}
```

## Performance Benefits

### Go vs Python
- **Speed**: 10-20x faster execution
- **Memory**: 50-70% lower memory usage  
- **Deployment**: Single binary, no runtime dependencies
- **Concurrency**: Better handling of concurrent log processing
- **Integration**: Native fit with Go-based payment infrastructure

## Troubleshooting

### LLM Connection Issues
```bash
# Test LLM server
curl http://localhost:1234/v1/models

# Check if port is occupied
lsof -i :1234
```

### Go Build Issues
```bash
# Clean and rebuild
go clean
go mod tidy
go build -o ai-agent main.go
```

### Docker Network Issues
- The agent uses `network_mode: "host"` to access localhost:1234
- On Windows/Mac, you might need to use `host.docker.internal:1234` instead

### Permission Issues
```bash
# Fix log directory permissions
chmod 755 logs/
chmod 644 logs/*.json
```

## Development

### Building
```bash
# Development build
go build -o ai-agent main.go

# Production build with optimizations
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ai-agent main.go
```

### Testing
```bash
# Run with test data
go run main.go

# Check generated reports
ls -la report_*.json
```

## Support

For issues and questions:
1. Check the logs: `docker-compose logs -f ai-agent`
2. Test the LLM connection: `curl http://localhost:1234/v1/models`
3. Verify configuration: `cat ai_config.json`
4. Build and run locally: `go run main.go`
