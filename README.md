# General AI Agent Structure

A flexible, modular Go-based framework for building AI-powered analysis agents. This structure allows you to quickly create specialized agents for different domains like fintech, e-commerce, healthcare, or general system monitoring.

## Features

- 🏗️ **Modular Architecture**: Plugin-based system for easy customization and extension
- 🤖 **Multiple AI Providers**: Support for OpenAI-compatible APIs, Anthropic Claude, and more
- 📊 **Domain-Specific Templates**: Pre-built configurations for fintech, e-commerce, healthcare, and general use
- 🔧 **Configuration Generator**: Easy-to-use tool for creating custom agent configurations
- 📁 **Flexible Data Sources**: JSON files, databases, APIs, and custom data sources
- 🔔 **Multi-Channel Notifications**: Console, webhooks, Slack, email, and custom handlers
- 📈 **Intelligent Analysis**: Customizable failure detection and AI-powered categorization
- ⚡ **High Performance**: Go-based for fast processing and low resource usage
- 🐳 **Docker Support**: Easy deployment with containerization

## Quick Start

### 1. Generate Configuration
Use the built-in configuration generator to create an agent for your domain:

```bash
# List available templates
go run cmd/config-generator/main.go list

# Generate a fintech agent configuration
go run cmd/config-generator/main.go generate fintech

# Generate an e-commerce agent configuration
go run cmd/config-generator/main.go generate ecommerce my_ecommerce_config.json

# Generate a general purpose agent
go run cmd/config-generator/main.go generate general
```

### 2. Setup AI Provider
You need a local LLM server running. Compatible with:

- **LM Studio** (recommended)
- **Ollama**
- **Text Generation WebUI**
- **vLLM**
- Any OpenAI-compatible local server

#### Setup LM Studio (Recommended)
1. Download and install [LM Studio](https://lmstudio.ai/)
2. Download a model (e.g., Qwen 3.5 4B)
3. Start the local server on port 1234
4. Test with: `curl http://localhost:1234/v1/models`

#### Setup Ollama Alternative
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Run a model
ollama run qwen2.5:4b

# Serve API on port 1234
ollama serve --host 0.0.0.0:1234
```

### 3. Prepare Data
Prepare your data files according to your configuration:

```bash
# For JSON file data source (default)
# Ensure you have logs.json and logs/ directory with detailed logs
ls logs.json logs/

# Example logs.json structure:
{
  "page": 1,
  "per_page": 50,
  "total": 100,
  "total_pages": 2,
  "rows": [
    {
      "id": "unique-id",
      "title": "Error Title",
      "entity": "service-name",
      "type": "error",
      "message": "Error description",
      "location": "service.method"
    }
  ]
}
```

### 4. Run the Agent

```bash
# Install Go dependencies
go mod tidy

# Run with default config
go run main.go

# Run with custom config
go run main.go my_custom_config.json

# Or build and run binary
go build -o ai-agent main.go
./ai-agent my_config.json
```

### 5. Run with Docker
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f ai-agent

# Stop
docker-compose down
```

## Architecture

The agent framework consists of several modular components:

### Core Components

- **Agent**: Main orchestrator that coordinates all components
- **DataSource**: Handles data input (JSON files, databases, APIs)
- **AIProvider**: Interfaces with AI services (OpenAI-compatible, Anthropic)
- **PromptBuilder**: Constructs domain-specific prompts for AI analysis
- **FailureDetector**: Identifies failures using keywords, regex, or ML
- **OutputHandler**: Manages analysis results (files, databases, APIs)
- **NotificationHandler**: Sends alerts (console, webhooks, Slack, email)

### Plugin System

Each component is pluggable and can be easily extended:

```go
// Create custom components
customDataSource := &MyCustomDataSource{}
customAIProvider := &MyCustomAIProvider{}

// Build agent with custom components
agent := factory.NewAgentBuilder().
    WithConfig("config.json").
    WithDataSource("custom").
    WithAIProvider("custom").
    Build()
```

## Configuration

The configuration system is flexible and supports multiple domains. Here's an example configuration:

```json
{
  "agent": {
    "name": "my-agent",
    "version": "1.0.0",
    "type": "log_analyzer",
    "description": "Custom AI agent"
  },
  "data_sources": {
    "primary": {
      "type": "json_file",
      "path": "logs.json",
      "format": "log_index",
      "detailed_logs_path": "logs/"
    }
  },
  "ai_provider": {
    "type": "openai_compatible",
    "api_url": "http://localhost:1234/v1/chat/completions",
    "model_name": "qwen/qwen3-4b-thinking-2507",
    "temperature": 0.1,
    "max_tokens": 2000
  },
  "analysis": {
    "domain": "general",
    "prompt_template": "default",
    "categories": ["infrastructure", "application", "security"]
  }
}
```

## Available Templates

### Fintech Template
Specialized for payment systems and financial services:
- **Domain**: Payment processing, acquiring/issuing operations
- **Categories**: acquiring, issuing, infrastructure, compliance, risk
- **Keywords**: payment, transaction, card, authorization, settlement

### E-commerce Template
Optimized for online retail platforms:
- **Domain**: E-commerce operations and customer experience
- **Categories**: order_management, customer_experience, payment, infrastructure
- **Keywords**: cart, checkout, order, inventory, shipping

### General Template
General purpose system monitoring:
- **Domain**: Infrastructure and application monitoring
- **Categories**: infrastructure, application, security, performance
- **Keywords**: error, exception, timeout, failure, critical

## File Structure

```
├── main.go                    # Main application entry point
├── go.mod                     # Go module dependencies
├── ai_config.json            # Default configuration file
├── logs.json                 # Sample data file
├── logs/                     # Detailed log files directory
├── reports/                  # Generated analysis reports
├── cmd/
│   └── config-generator/     # Configuration generator tool
├── pkg/
│   ├── agent/               # Core agent interfaces and base implementation
│   ├── aiprovider/          # AI provider implementations
│   ├── config/              # Configuration management
│   ├── datasource/          # Data source implementations
│   ├── detector/            # Failure detection implementations
│   ├── factory/             # Component factory and builder
│   ├── notification/        # Notification handler implementations
│   ├── output/              # Output handler implementations
│   └── prompt/              # Prompt builder implementations
├── Dockerfile               # Docker configuration
└── docker-compose.yml       # Docker Compose setup
```

## How It Works

1. **Initialize**: Load configuration and initialize all components
2. **Monitor**: Scan data sources for new entries based on check interval
3. **Detect**: Use failure detectors to identify issues (keywords, patterns, ML)
4. **Analyze**: Send detected failures to AI provider with domain-specific prompts
5. **Categorize**: Classify issues by domain categories and severity levels
6. **Report**: Generate detailed analysis reports with business impact assessment
7. **Notify**: Send alerts through configured notification channels
8. **Store**: Save results using configured output handlers

## Creating Custom Agents

### 1. Using Configuration Generator
The easiest way to create a custom agent:

```bash
# Generate base configuration
go run cmd/config-generator/main.go generate general my_agent_config.json

# Edit the generated configuration
vim my_agent_config.json

# Run your custom agent
go run main.go my_agent_config.json
```

### 2. Custom Components
Create custom components by implementing the interfaces:

```go
// Custom data source
type MyDataSource struct{}

func (ds *MyDataSource) Initialize(config map[string]interface{}) error {
    // Initialize your data source
    return nil
}

func (ds *MyDataSource) GetEntries(ctx context.Context) ([]agent.DataEntry, error) {
    // Return data entries to analyze
    return entries, nil
}

// Custom AI provider
type MyAIProvider struct{}

func (ai *MyAIProvider) Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*agent.Analysis, error) {
    // Perform AI analysis
    return analysis, nil
}
```

### 3. Custom Prompt Templates
Create domain-specific prompt templates:

```go
promptBuilder := prompt.NewTemplateBuilder("my_domain")
promptBuilder.SetTemplate(`
You are an expert in {{.Domain}} analysis.
Analyze this entry: {{.Entry.Message}}
Provide analysis in JSON format...
`)
```

## Sample AI Analysis Output

```json
{
    "error_type": "Vpos Submerchant Payment Processor Information Retrieval Failure",
    "severity": "high",
    "category": "acquiring",
    "description": "System failed to retrieve payment processor configuration for submerchant during Vpos processing",
    "root_cause": "Invalid submerchant configuration or network connectivity issues with payment processor",
    "business_impact": "Failure to process payments for affected submerchants, potential revenue loss",
    "suggested_action": "Verify submerchant configuration, check network connectivity to payment processor",
    "is_retryable": true,
    "compliance_risk": "medium",
    "customer_impact": "Customers unable to complete payments; potential transaction failures"
}
```

## Performance Benefits

### Go vs Python
- **Speed**: 10-20x faster execution
- **Memory**: 50-70% lower memory usage  
- **Deployment**: Single binary, no runtime dependencies
- **Concurrency**: Better handling of concurrent log processing
- **Integration**: Native fit with Go-based payment infrastructure

## Troubleshooting

### LLM Connection Issues
```bash
# Test LLM server
curl http://localhost:1234/v1/models

# Check if port is occupied
lsof -i :1234
```

### Go Build Issues
```bash
# Clean and rebuild
go clean
go mod tidy
go build -o ai-agent main.go
```

### Docker Network Issues
- The agent uses `network_mode: "host"` to access localhost:1234
- On Windows/Mac, you might need to use `host.docker.internal:1234` instead

### Permission Issues
```bash
# Fix log directory permissions
chmod 755 logs/
chmod 644 logs/*.json
```

## Development

### Building
```bash
# Development build
go build -o ai-agent main.go

# Production build with optimizations
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ai-agent main.go
```

### Testing
```bash
# Run with test data
go run main.go

# Check generated reports
ls -la report_*.json
```

## Support

For issues and questions:
1. Check the logs: `docker-compose logs -f ai-agent`
2. Test the LLM connection: `curl http://localhost:1234/v1/models`
3. Verify configuration: `cat ai_config.json`
4. Build and run locally: `go run main.go`
