version: '3.8'

services:
  ai-agent:
    build: .
    container_name: ai-order-failure-agent
    network_mode: "host"  # To access localhost:1234 (local LLM)
    volumes:
      - ./logs:/root/logs
      - ./ai_config.json:/root/ai_config.json
      - ./logs.json:/root/logs.json
      - ./order.json:/root/order.json
      - ./reports:/root/reports
    restart: unless-stopped
    
  # Optional: Redis for job queue (retry mechanism)
  redis:
    image: redis:7-alpine
    container_name: agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Optional: PostgreSQL for persistence
  postgres:
    image: postgres:15-alpine
    container_name: agent-db
    environment:
      POSTGRES_DB: agent
      POSTGRES_USER: agent
      POSTGRES_PASSWORD: agent_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
