package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"general-ai-agent-structure/pkg/factory"
)

func main() {
	// Check for config file argument
	configPath := "ai_config.json"
	if len(os.Args) > 1 {
		configPath = os.Args[1]
	}

	// Check if config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("Configuration file not found: %s\n", configPath)
		fmt.Println("Usage: go run main.go [config_file]")
		os.Exit(1)
	}

	// Create agent using factory
	builder := factory.NewAgentBuilder()
	agent := builder.BuildDefault(configPath)

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\nReceived shutdown signal, stopping agent...")
		cancel()
	}()

	// Start the agent
	fmt.Printf("🤖 Starting General AI Agent...\n")
	if err := agent.Start(ctx); err != nil {
		fmt.Printf("Failed to start agent: %v\n", err)
		os.Exit(1)
	}

	// Wait for context cancellation
	<-ctx.Done()

	// Stop the agent
	if err := agent.Stop(); err != nil {
		fmt.Printf("Error stopping agent: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("🎉 Agent stopped successfully!")
}
