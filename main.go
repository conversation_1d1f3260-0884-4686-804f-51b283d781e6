package main

import (
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/sirupsen/logrus"
)

// Config represents the agent configuration
type Config struct {
	LocalLLMEndpoint string  `json:"api_url"`
	Model            string  `json:"model_name"`
	MaxTokens        int     `json:"max_tokens"`
	Temperature      float64 `json:"temperature"`
}

// LogEntry represents a log entry structure
type LogEntry struct {
	ID       string `json:"id"`
	Title    string `json:"title"`
	Entity   string `json:"entity"`
	Type     string `json:"type"`
	Proto    string `json:"proto"`
	Message  string `json:"message"`
	Location string `json:"location"`
}

// LogIndex represents the logs.json structure
type LogIndex struct {
	Page       int        `json:"page"`
	PerPage    int        `json:"per_page"`
	Total      int        `json:"total"`
	TotalPages int        `json:"total_pages"`
	Rows       []LogEntry `json:"rows"`
}

// AIAnalysis represents the AI analysis result
type AIAnalysis struct {
	ErrorType       string `json:"error_type"`
	Severity        string `json:"severity"`
	Category        string `json:"category"`
	Description     string `json:"description"`
	RootCause       string `json:"root_cause"`
	BusinessImpact  string `json:"business_impact"`
	SuggestedAction string `json:"suggested_action"`
	IsRetryable     bool   `json:"is_retryable"`
	ComplianceRisk  string `json:"compliance_risk"`
	CustomerImpact  string `json:"customer_impact"`
}

// Agent represents the order failure analysis agent
type Agent struct {
	config *Config
	client *resty.Client
	logger *logrus.Logger
}

// NewAgent creates a new agent instance
func NewAgent() (*Agent, error) {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	config, err := loadConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	client := resty.New()
	client.SetTimeout(60 * time.Second)

	return &Agent{
		config: config,
		client: client,
		logger: logger,
	}, nil
}

// loadConfig loads configuration from ai_config.json
func loadConfig() (*Config, error) {
	data, err := os.ReadFile("ai_config.json")
	if err != nil {
		return nil, err
	}

	var config Config
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// loadLogs loads the log index from logs.json
func (a *Agent) loadLogs() (*LogIndex, error) {
	data, err := os.ReadFile("logs.json")
	if err != nil {
		return nil, err
	}

	var logIndex LogIndex
	err = json.Unmarshal(data, &logIndex)
	if err != nil {
		return nil, err
	}

	return &logIndex, nil
}

// loadDetailedLog loads a detailed log file using array index
func (a *Agent) loadDetailedLog(logIndex int) (map[string]interface{}, error) {
	filename := fmt.Sprintf("logs/%d.json", logIndex+1) // files are 1-indexed
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var detailedLog map[string]interface{}
	err = json.Unmarshal(data, &detailedLog)
	if err != nil {
		return nil, err
	}

	return detailedLog, nil
}

// analyzeWithAI performs AI analysis of the log entry
func (a *Agent) analyzeWithAI(logEntry LogEntry, detailedLog map[string]interface{}) (*AIAnalysis, error) {
	prompt := a.buildFinTechPrompt(logEntry, detailedLog)

	type OpenAIRequest struct {
		Model       string              `json:"model"`
		Messages    []map[string]string `json:"messages"`
		MaxTokens   int                 `json:"max_tokens"`
		Temperature float64             `json:"temperature"`
	}

	request := OpenAIRequest{
		Model: a.config.Model,
		Messages: []map[string]string{
			{
				"role":    "user",
				"content": prompt,
			},
		},
		MaxTokens:   a.config.MaxTokens,
		Temperature: a.config.Temperature,
	}

	resp, err := a.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(request).
		Post(a.config.LocalLLMEndpoint)

	if err != nil {
		return nil, fmt.Errorf("API request failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	type OpenAIResponse struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	}

	var apiResponse OpenAIResponse
	err = json.Unmarshal(resp.Body(), &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to parse API response: %w", err)
	}

	if len(apiResponse.Choices) == 0 {
		return nil, fmt.Errorf("no choices in API response")
	}

	aiContent := apiResponse.Choices[0].Message.Content
	a.logger.Infof("Raw AI Response:\n%s", aiContent)

	// Extract JSON from the AI response
	analysis, err := a.extractJSONFromResponse(aiContent)
	if err != nil {
		a.logger.Warnf("Failed to parse AI JSON response: %v", err)
		return a.fallbackAnalysis(logEntry), nil
	}

	return analysis, nil
}

// extractJSONFromResponse extracts JSON from AI response that may contain thinking tags
func (a *Agent) extractJSONFromResponse(text string) (*AIAnalysis, error) {
	// Remove think tags
	thinkPattern := regexp.MustCompile(`(?s)<think>.*?</think>`)
	cleanedText := thinkPattern.ReplaceAllString(text, "")

	// Clean up the text - remove extra whitespace and normalize
	cleanedText = regexp.MustCompile(`\s+`).ReplaceAllString(cleanedText, " ")
	cleanedText = strings.TrimSpace(cleanedText)

	// Find JSON object using bracket matching
	startIdx := strings.Index(cleanedText, "{")
	if startIdx == -1 {
		return nil, fmt.Errorf("no JSON object found")
	}

	bracketCount := 0
	endIdx := startIdx

	for i, char := range cleanedText[startIdx:] {
		if char == '{' {
			bracketCount++
		} else if char == '}' {
			bracketCount--
			if bracketCount == 0 {
				endIdx = startIdx + i + 1
				break
			}
		}
	}

	if bracketCount != 0 {
		return nil, fmt.Errorf("unmatched brackets in JSON")
	}

	jsonStr := cleanedText[startIdx:endIdx]

	// Clean up common JSON formatting issues
	jsonStr = regexp.MustCompile(`\s+`).ReplaceAllString(jsonStr, " ")
	jsonStr = regexp.MustCompile(`,\s*}`).ReplaceAllString(jsonStr, "}")
	jsonStr = regexp.MustCompile(`,\s*]`).ReplaceAllString(jsonStr, "]")

	var analysis AIAnalysis
	err := json.Unmarshal([]byte(jsonStr), &analysis)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	return &analysis, nil
}

// buildFinTechPrompt builds a comprehensive prompt for fintech analysis
func (a *Agent) buildFinTechPrompt(logEntry LogEntry, detailedLog map[string]interface{}) string {
	detailedLogJSON, _ := json.MarshalIndent(detailedLog, "", "  ")

	return fmt.Sprintf(`You are an expert fintech operations analyst specializing in payment systems, acquiring operations, and issuing services. 

**FINTECH DOMAIN EXPERTISE:**

**ACQUIRING OPERATIONS:**
- Merchant onboarding and underwriting processes
- Payment processing flows (authorization, capture, settlement)
- Risk management and fraud detection systems
- Scheme compliance (Visa, Mastercard, regional networks)
- PCI DSS compliance and tokenization
- Multi-acquirer routing and smart routing logic
- Transaction monitoring and suspicious activity detection
- Chargeback and dispute management
- Multi-currency processing and FX handling
- Reconciliation and settlement processes

**ISSUING OPERATIONS:**
- Card issuing and provisioning
- Digital wallet integration (Apple Pay, Google Pay, Samsung Pay)
- Real-time authorization and transaction monitoring
- Spending controls and limits management
- KYC/AML for cardholders
- Card lifecycle management (activation, blocking, renewal)
- PIN management and card security
- Statement generation and customer communications

**FINTECH INFRASTRUCTURE:**
- API gateway patterns and rate limiting
- Microservices architecture for payments
- Database consistency and ACID transactions
- Event-driven architectures and message queues
- Real-time fraud detection and machine learning
- Regulatory compliance (PSD2, Open Banking, GDPR)

**COMMON PAYMENT SYSTEM ERRORS:**
- Authorization failures (insufficient funds, expired cards, velocity limits)
- Network timeouts and connectivity issues
- Scheme downtimes and maintenance windows
- Configuration errors (merchant setup, routing rules)
- Compliance violations and risk threshold breaches
- Settlement and reconciliation discrepancies

Analyze this payment system log entry and provide a detailed technical analysis:

**LOG ENTRY:**
ID: %s
Title: %s
Entity: %s
Type: %s
Protocol: %s
Message: %s
Location: %s

**DETAILED LOG:**
%s

Based on your fintech expertise, provide analysis in valid JSON format with these exact fields:
{
  "error_type": "string - specific technical classification",
  "severity": "string - low/medium/high/critical",
  "category": "string - acquiring/issuing/infrastructure/compliance/risk",
  "description": "string - detailed technical explanation",
  "root_cause": "string - likely underlying cause",
  "business_impact": "string - impact on operations",
  "suggested_action": "string - specific remediation steps",
  "is_retryable": boolean - whether operation can be retried,
  "compliance_risk": "string - none/low/medium/high",
  "customer_impact": "string - effect on end customers"
}

Respond with ONLY valid JSON. No additional text, explanations, or thinking process.`,
		logEntry.ID, logEntry.Title, logEntry.Entity, logEntry.Type,
		logEntry.Proto, logEntry.Message, logEntry.Location, detailedLogJSON)
}

// fallbackAnalysis provides a fallback analysis when AI fails
func (a *Agent) fallbackAnalysis(logEntry LogEntry) *AIAnalysis {
	severity := "medium"
	category := "infrastructure"
	isRetryable := true

	// Determine severity based on log type and content
	if logEntry.Type == "error" || strings.Contains(strings.ToLower(logEntry.Message), "error") {
		severity = "high"
	}

	// Determine category based on content
	message := strings.ToLower(logEntry.Message + " " + logEntry.Title)
	if strings.Contains(message, "payment") || strings.Contains(message, "transaction") {
		category = "acquiring"
	} else if strings.Contains(message, "card") || strings.Contains(message, "issuing") {
		category = "issuing"
	} else if strings.Contains(message, "compliance") || strings.Contains(message, "kyc") {
		category = "compliance"
	}

	return &AIAnalysis{
		ErrorType:       fmt.Sprintf("System Error: %s", logEntry.Title),
		Severity:        severity,
		Category:        category,
		Description:     fmt.Sprintf("Error detected in %s: %s", logEntry.Entity, logEntry.Message),
		RootCause:       "System error requiring investigation",
		BusinessImpact:  "Potential service disruption",
		SuggestedAction: "Review logs and system status",
		IsRetryable:     isRetryable,
		ComplianceRisk:  "low",
		CustomerImpact:  "Possible service interruption",
	}
}

// saveReport saves the analysis report to a file
func (a *Agent) saveReport(logEntry LogEntry, analysis *AIAnalysis) error {
	report := map[string]interface{}{
		"timestamp":    time.Now().Format(time.RFC3339),
		"log_entry":    logEntry,
		"analysis":     analysis,
		"processed_by": "AI Agent Go",
	}

	filename := fmt.Sprintf("report_%s_%d.json", logEntry.ID, time.Now().Unix())
	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(filename, data, 0644)
}

// sendNotification sends notification (placeholder)
func (a *Agent) sendNotification(logEntry LogEntry, analysis *AIAnalysis) {
	a.logger.Infof("🚨 ALERT: %s (Severity: %s)", analysis.ErrorType, analysis.Severity)
	a.logger.Infof("📋 Category: %s | Retryable: %t", analysis.Category, analysis.IsRetryable)
	a.logger.Infof("💼 Business Impact: %s", analysis.BusinessImpact)
	a.logger.Infof("🎯 Suggested Action: %s", analysis.SuggestedAction)
}

// processLogs processes all logs and analyzes failures
func (a *Agent) processLogs() error {
	logIndex, err := a.loadLogs()
	if err != nil {
		return fmt.Errorf("failed to load logs: %w", err)
	}

	a.logger.Infof("🔍 Processing %d log entries...", len(logIndex.Rows))

	failureCount := 0
	for i, logEntry := range logIndex.Rows {
		// Check if this is a failure (error type or contains error keywords)
		if a.isFailure(logEntry) {
			failureCount++
			a.logger.Infof("⚠️  Processing failure: %s - %s", logEntry.ID, logEntry.Title)

			// Load detailed log
			detailedLog, err := a.loadDetailedLog(i)
			if err != nil {
				a.logger.Warnf("Failed to load detailed log for %s: %v", logEntry.ID, err)
				continue
			}

			// Analyze with AI
			analysis, err := a.analyzeWithAI(logEntry, detailedLog)
			if err != nil {
				a.logger.Errorf("Failed to analyze log %s: %v", logEntry.ID, err)
				continue
			}

			// Save report
			err = a.saveReport(logEntry, analysis)
			if err != nil {
				a.logger.Errorf("Failed to save report for %s: %v", logEntry.ID, err)
			}

			// Send notification
			//a.sendNotification(logEntry, analysis)

			a.logger.Infof("✅ Completed analysis for %s", logEntry.ID)
		}
	}

	a.logger.Infof("🎯 Analysis complete. Processed %d failures out of %d total logs.", failureCount, len(logIndex.Rows))
	return nil
}

// isFailure determines if a log entry represents a failure
func (a *Agent) isFailure(logEntry LogEntry) bool {
	// Check log type
	if logEntry.Type == "error" || logEntry.Type == "critical" {
		return true
	}

	// Check for error keywords in title or message
	content := strings.ToLower(logEntry.Title + " " + logEntry.Message)
	errorKeywords := []string{"error", "failed", "failure", "exception", "timeout", "rejected", "declined", "invalid"}

	for _, keyword := range errorKeywords {
		if strings.Contains(content, keyword) {
			return true
		}
	}

	return false
}

func main() {

	agent, err := NewAgent()
	if err != nil {
		fmt.Printf("Failed to create agent: %v\n", err)
		os.Exit(1)
	}
	agent.logger.Info("🤖 AI Order Failure Analysis Agent (Go) Starting...")

	err = agent.processLogs()
	if err != nil {
		agent.logger.Errorf("Failed to process logs: %v", err)
		os.Exit(1)
	}

	agent.logger.Info("🎉 Agent completed successfully!")
}
