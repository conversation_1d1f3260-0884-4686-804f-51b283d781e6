package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"general-ai-agent-structure/pkg/config"
)

// ConfigTemplate represents a configuration template
type ConfigTemplate struct {
	Name        string
	Description string
	Config      *config.Config
}

// getTemplates returns predefined configuration templates
func getTemplates() map[string]ConfigTemplate {
	return map[string]ConfigTemplate{
		"fintech": {
			Name:        "Fintech Payment Analysis",
			Description: "Specialized agent for analyzing payment system logs and financial transactions",
			Config: &config.Config{
				Agent: config.AgentInfo{
					Name:        "fintech-payment-analyzer",
					Version:     "1.0.0",
					Type:        "log_analyzer",
					Description: "AI agent for fintech payment system analysis",
				},
				DataSources: map[string]config.DataSource{
					"primary": {
						Type:             "json_file",
						Path:             "logs.json",
						Format:           "log_index",
						DetailedLogsPath: "logs/",
					},
				},
				AIProvider: config.AIProvider{
					Type:           "openai_compatible",
					APIURL:         "http://localhost:1234/v1/chat/completions",
					ModelName:      "qwen/qwen3-4b-thinking-2507",
					Temperature:    0.1,
					MaxTokens:      2000,
					TimeoutSeconds: 60,
				},
				Processing: config.Processing{
					CheckInterval:      30,
					MaxRetries:         3,
					BatchSize:          10,
					ParallelProcessing: false,
					FailureDetection: config.FailureDetection{
						Enabled:  true,
						Keywords: []string{"error", "failed", "failure", "timeout", "rejected", "declined", "invalid"},
						LogTypes: []string{"error", "critical"},
					},
				},
				Analysis: config.Analysis{
					Domain:            "fintech",
					PromptTemplate:    "fintech",
					SeverityThreshold: "medium",
					Categories:        []string{"acquiring", "issuing", "infrastructure", "compliance", "risk"},
					OutputFormat:      "json",
				},
				Notifications: config.Notifications{
					Enabled: true,
					Channels: map[string]config.NotificationChannel{
						"console": {Enabled: true},
						"webhook": {Enabled: false},
						"slack":   {Enabled: false},
					},
				},
				Output: config.Output{
					ReportsDirectory: "reports/",
					FilenamePattern:  "analysis_{id}_{timestamp}.json",
					IncludeRawData:   false,
					PrettyPrint:      true,
				},
			},
		},
		"ecommerce": {
			Name:        "E-commerce System Analysis",
			Description: "Agent for analyzing e-commerce platform logs and customer experience issues",
			Config: &config.Config{
				Agent: config.AgentInfo{
					Name:        "ecommerce-system-analyzer",
					Version:     "1.0.0",
					Type:        "log_analyzer",
					Description: "AI agent for e-commerce system analysis",
				},
				DataSources: map[string]config.DataSource{
					"primary": {
						Type:             "json_file",
						Path:             "logs.json",
						Format:           "log_index",
						DetailedLogsPath: "logs/",
					},
				},
				AIProvider: config.AIProvider{
					Type:           "openai_compatible",
					APIURL:         "http://localhost:1234/v1/chat/completions",
					ModelName:      "qwen/qwen3-4b-thinking-2507",
					Temperature:    0.1,
					MaxTokens:      2000,
					TimeoutSeconds: 60,
				},
				Processing: config.Processing{
					CheckInterval:      30,
					MaxRetries:         3,
					BatchSize:          10,
					ParallelProcessing: false,
					FailureDetection: config.FailureDetection{
						Enabled:  true,
						Keywords: []string{"error", "failed", "failure", "timeout", "cart", "checkout", "payment"},
						LogTypes: []string{"error", "critical"},
					},
				},
				Analysis: config.Analysis{
					Domain:            "ecommerce",
					PromptTemplate:    "ecommerce",
					SeverityThreshold: "medium",
					Categories:        []string{"order_management", "customer_experience", "payment", "infrastructure"},
					OutputFormat:      "json",
				},
				Notifications: config.Notifications{
					Enabled: true,
					Channels: map[string]config.NotificationChannel{
						"console": {Enabled: true},
						"webhook": {Enabled: false},
						"slack":   {Enabled: false},
					},
				},
				Output: config.Output{
					ReportsDirectory: "reports/",
					FilenamePattern:  "analysis_{id}_{timestamp}.json",
					IncludeRawData:   false,
					PrettyPrint:      true,
				},
			},
		},
		"general": {
			Name:        "General System Analysis",
			Description: "General purpose agent for analyzing system logs and infrastructure issues",
			Config: &config.Config{
				Agent: config.AgentInfo{
					Name:        "general-system-analyzer",
					Version:     "1.0.0",
					Type:        "log_analyzer",
					Description: "General purpose AI agent for system analysis",
				},
				DataSources: map[string]config.DataSource{
					"primary": {
						Type:             "json_file",
						Path:             "logs.json",
						Format:           "log_index",
						DetailedLogsPath: "logs/",
					},
				},
				AIProvider: config.AIProvider{
					Type:           "openai_compatible",
					APIURL:         "http://localhost:1234/v1/chat/completions",
					ModelName:      "qwen/qwen3-4b-thinking-2507",
					Temperature:    0.1,
					MaxTokens:      2000,
					TimeoutSeconds: 60,
				},
				Processing: config.Processing{
					CheckInterval:      30,
					MaxRetries:         3,
					BatchSize:          10,
					ParallelProcessing: false,
					FailureDetection: config.FailureDetection{
						Enabled:  true,
						Keywords: []string{"error", "failed", "failure", "exception", "timeout", "critical"},
						LogTypes: []string{"error", "critical"},
					},
				},
				Analysis: config.Analysis{
					Domain:            "general",
					PromptTemplate:    "general",
					SeverityThreshold: "medium",
					Categories:        []string{"infrastructure", "application", "security", "performance"},
					OutputFormat:      "json",
				},
				Notifications: config.Notifications{
					Enabled: true,
					Channels: map[string]config.NotificationChannel{
						"console": {Enabled: true},
						"webhook": {Enabled: false},
						"slack":   {Enabled: false},
					},
				},
				Output: config.Output{
					ReportsDirectory: "reports/",
					FilenamePattern:  "analysis_{id}_{timestamp}.json",
					IncludeRawData:   false,
					PrettyPrint:      true,
				},
			},
		},
		"webhook": {
			Name:        "Real-time Webhook Analysis",
			Description: "Real-time agent for processing webhook data streams",
			Config: &config.Config{
				Agent: config.AgentInfo{
					Name:        "webhook-realtime-analyzer",
					Version:     "1.0.0",
					Type:        "realtime_analyzer",
					Description: "Real-time AI agent for webhook-based data analysis",
				},
				DataSources: map[string]config.DataSource{
					"primary": {
						Type:             "webhook",
						Path:             "",
						Format:           "webhook",
						DetailedLogsPath: "",
					},
				},
				AIProvider: config.AIProvider{
					Type:           "openai_compatible",
					APIURL:         "http://localhost:1234/v1/chat/completions",
					ModelName:      "qwen/qwen3-4b-thinking-2507",
					Temperature:    0.1,
					MaxTokens:      2000,
					TimeoutSeconds: 60,
				},
				Processing: config.Processing{
					CheckInterval:      5,
					MaxRetries:         3,
					BatchSize:          20,
					ParallelProcessing: false,
					FailureDetection: config.FailureDetection{
						Enabled:  true,
						Keywords: []string{"error", "failed", "failure", "exception", "timeout", "critical", "alert"},
						LogTypes: []string{"error", "critical", "alert"},
					},
				},
				Analysis: config.Analysis{
					Domain:            "general",
					PromptTemplate:    "general",
					SeverityThreshold: "medium",
					Categories:        []string{"infrastructure", "application", "security", "performance", "webhook"},
					OutputFormat:      "json",
				},
				Notifications: config.Notifications{
					Enabled: true,
					Channels: map[string]config.NotificationChannel{
						"console": {Enabled: true},
						"webhook": {Enabled: false},
						"slack":   {Enabled: false},
					},
				},
				Output: config.Output{
					ReportsDirectory: "reports/",
					FilenamePattern:  "webhook_analysis_{id}_{timestamp}.json",
					IncludeRawData:   true,
					PrettyPrint:      true,
				},
			},
		},
	}
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	command := os.Args[1]

	switch command {
	case "list":
		listTemplates()
	case "generate":
		if len(os.Args) < 3 {
			fmt.Println("Error: template name required")
			fmt.Println("Usage: config-generator generate <template_name> [output_file]")
			os.Exit(1)
		}
		templateName := os.Args[2]
		outputFile := "ai_config.json"
		if len(os.Args) > 3 {
			outputFile = os.Args[3]
		}
		generateConfig(templateName, outputFile)
	case "interactive":
		interactiveMode()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("AI Agent Configuration Generator")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  config-generator list                              - List available templates")
	fmt.Println("  config-generator generate <template> [output]      - Generate config from template")
	fmt.Println("  config-generator interactive                       - Interactive configuration")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  config-generator list")
	fmt.Println("  config-generator generate fintech")
	fmt.Println("  config-generator generate general my_config.json")
	fmt.Println("  config-generator interactive")
}

func listTemplates() {
	templates := getTemplates()

	fmt.Println("Available Configuration Templates:")
	fmt.Println()

	for name, template := range templates {
		fmt.Printf("📋 %s\n", strings.ToUpper(name))
		fmt.Printf("   Name: %s\n", template.Name)
		fmt.Printf("   Description: %s\n", template.Description)
		fmt.Printf("   Domain: %s\n", template.Config.Analysis.Domain)
		fmt.Printf("   Categories: %s\n", strings.Join(template.Config.Analysis.Categories, ", "))
		fmt.Println()
	}
}

func generateConfig(templateName, outputFile string) {
	templates := getTemplates()

	template, exists := templates[templateName]
	if !exists {
		fmt.Printf("Error: Template '%s' not found\n", templateName)
		fmt.Println("Available templates:")
		for name := range templates {
			fmt.Printf("  - %s\n", name)
		}
		os.Exit(1)
	}

	// Create output directory if it doesn't exist
	outputDir := filepath.Dir(outputFile)
	if outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			fmt.Printf("Error creating output directory: %v\n", err)
			os.Exit(1)
		}
	}

	// Marshal config to JSON
	data, err := json.MarshalIndent(template.Config, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		os.Exit(1)
	}

	// Write to file
	if err := os.WriteFile(outputFile, data, 0644); err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Configuration generated successfully!\n")
	fmt.Printf("📁 Output file: %s\n", outputFile)
	fmt.Printf("🏷️  Template: %s\n", template.Name)
	fmt.Printf("📋 Description: %s\n", template.Description)
	fmt.Println()
	fmt.Println("Next steps:")
	fmt.Println("1. Review and customize the generated configuration")
	fmt.Println("2. Ensure your AI provider is running (e.g., LM Studio on localhost:1234)")
	fmt.Println("3. Prepare your data files (logs.json and logs/ directory)")
	fmt.Printf("4. Run the agent: go run main.go %s\n", outputFile)
}

func interactiveMode() {
	fmt.Println("🤖 Interactive AI Agent Configuration")
	fmt.Println("=====================================")
	fmt.Println()

	// TODO: Implement interactive configuration
	fmt.Println("Interactive mode is not implemented yet.")
	fmt.Println("Please use 'config-generator generate <template>' for now.")
}
