# Order Failure Analysis Agent - Go Version
FROM golang:1.25-alpine AS builder

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY main.go ./

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ai-agent main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/ai-agent .

# Copy configuration and data files
COPY ai_config.json .
COPY logs.json .
COPY order.json .

# Create directory for data and logs
RUN mkdir -p /root/data /root/logs

# Set environment variables
ENV DATA_DIR=/root/data

# Run the AI agent
CMD ["./ai-agent"]
